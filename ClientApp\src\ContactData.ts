import api from "./Api";
import { BASE_URL } from "./Context/config";

export interface PutContact {
  Id: number;
  Category: string;
  ContactInfo: string;
  Title?: string;
  isPublic: boolean;
}

export interface PostContact {
  UserId: number;
  ContactInfo: string;
  Category: string;
  Title?: string;
  isPublic: boolean;
}

export const EditContact = async (newContact: PutContact) => {
  const data = {
    Id: newContact.Id,
    Category: newContact.Category,
    ContactInfo: newContact.ContactInfo,
    Title: newContact.Title,
    isPublic: newContact.isPublic,
  };

  if (newContact.isPublic === true) data.isPublic = true;
  else data.isPublic = false;

  try {
    const response = await api.put(`${BASE_URL}/Contact/EditContact`, data, {
      headers: {
        "Content-Type": "application/json",
      },
    });

    return response;
  } catch (error) {
    console.error("EditContact Error:", error);

    if (error.response) {
      // Server responded with error status
      const errorMessage =
        error.response.data?.error ||
        error.response.data?.message ||
        `Server error: ${error.response.status}`;
      throw new Error(errorMessage);
    } else if (error.request) {
      // Network error
      throw new Error("Network error: Unable to connect to server");
    } else {
      // Other error
      throw new Error(error.message || "Unknown error occurred");
    }
  }
};

export const DeleteContact = async (Id: number) => {
  try {
    const response = await api.delete(
      `${BASE_URL}/Contact/DeleteContact/${Id}`,
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    return response;
  } catch (error) {
    throw error;
  }
};

export const CreateContact = async (newContact: PostContact) => {
  const data = {
    UserId: newContact.UserId,
    Category: newContact.Category || "CvFile",
    ContactInfo: newContact.ContactInfo,
    Title: newContact.Title,
    isPublic: newContact.isPublic,
  };

  if (newContact.isPublic === true) data.isPublic = true;
  else data.isPublic = false;

  console.log("CreateContact - Sending data:", data);
  console.log("CreateContact - ContactInfo length:", data.ContactInfo?.length);
  console.log("CreateContact - ContactInfo type:", typeof data.ContactInfo);

  try {
    const response = await api.post(`${BASE_URL}/Contact/CreateContact`, data, {
      headers: {
        "Content-Type": "application/json",
      },
    });
    return response;
  } catch (error) {
    console.error("Error creating contact:", error);
    console.error("Error response:", error.response?.data);
    console.error("Error status:", error.response?.status);
    throw error;
  }
};
