import api from "./Api";
import { BASE_URL } from "./Context/config";

export interface PostCoupon {
  UserId: number;
  Date: string;
  Country: string;
  Amount: number;
  Number: number;
}

export interface PostUseCoupon {
  SerialKey: string;
  Skill_QualityOfWork: number;
  Skill_CostEffectiveness: number;
  Skill_Timeliness: number;
  Skill_Communication: number;
  Skill_Agility: number;
}

export const PurchasingCoupon = async (coupon: PostCoupon) => {
  const data = {
    UserId: coupon.UserId,
    Date: coupon.Date,
    Country: coupon.Country,
    Amount: coupon.Amount,
    Number: coupon.Number,
  };

  try {
    var authToken = getAuthToken();
    const response = await api.post(
      `${BASE_URL}/Purchases/PurchasingCoupon`,
      data,
      {
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
      }
    );
    return response;
  } catch (error) {
    console.error("Error purchasing coupon:", error);
    if (error.response && error.response.data) {
      if (typeof error.response.data === "string") {
        throw new Error(error.response.data);
      }
      if (error.response.data.error) {
        throw new Error(error.response.data.error);
      }
    }
    // Provide user-friendly error messages based on status codes
    if (error.response) {
      switch (error.response.status) {
        case 400:
          throw new Error(
            "Insufficient funds or invalid purchase data. Please check your balance and try again."
          );
        case 401:
          throw new Error("Authentication failed. Please log in again.");
        case 500:
          throw new Error(
            "Server error while purchasing coupon. Please try again later."
          );
        default:
          throw new Error("Failed to purchase coupon. Please try again.");
      }
    }
    throw new Error(
      "Network error. Please check your internet connection and try again."
    );
  }
};

export const GetCoupons = async () => {
  try {
    var authToken = getAuthToken();
    const response = await api.get(`${BASE_URL}/Coupons/GetCoupons`, {
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
      },
    });

    const modifiedResponse = response.data.map((item) => {
      const { customer, ...rest } = item;
      if (customer != null) {
        const customerInfo = {
          customerUserName: customer.userName,
          customerFirstName: customer.firstName,
          customerLastName: customer.lastName,
          customerProfilePicture: customer.profilePicture,
        };
        return { ...rest, ...customerInfo };
      }
      return item;
    });

    return modifiedResponse;
  } catch (error) {
    console.error("Error:", error.message);
  }
};

export const GetCustomerCoupons = async () => {
  try {
    var authToken = getAuthToken();
    const response = await api.get(`${BASE_URL}/Coupons/GetCustomerCoupons`, {
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
      },
    });

    const modifiedResponse = response.data.map((item) => {
      const { owner, ...rest } = item;
      const ownerInfo = {
        ownerId: owner.id,
        ownerUserName: owner.userName,
        ownerFirstName: owner.firstName,
        ownerLastName: owner.lastName,
        ownerProfilePicture: owner.profilePicture,
        ownerEmail: owner.email,
        ownerCategory: owner.category,
      };
      return { ...rest, ...ownerInfo };
    });

    return modifiedResponse;
  } catch (error) {
    return { error: error.message };
  }
};

export const GetCouponsFromCustomers = async () => {
  try {
    var authToken = getAuthToken();
    const response = await api.get(
      `${BASE_URL}/Coupons/GetCouponsFromCustomers`,
      {
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    return response;
  } catch (error) {
    return { error: error.message };
  }
};

export const ReserveCoupon = async (serialKey: string) => {
  try {
    // Input validation
    if (
      !serialKey ||
      typeof serialKey !== "string" ||
      serialKey.trim() === ""
    ) {
      throw new Error("Serial key is required and must be a valid string.");
    }

    var authToken = getAuthToken();
    if (!authToken) {
      throw new Error("Authentication required. Please log in and try again.");
    }

    const response = await api.put(
      `${BASE_URL}/Coupons/ReserveCoupon/${encodeURIComponent(
        serialKey.trim()
      )}`,
      {}, // Empty body for PUT request
      {
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    return response;
  } catch (error: any) {
    // Enhanced error handling
    if (error.response) {
      // Server responded with error status
      const errorMessage =
        error.response.data?.error ||
        error.response.data?.message ||
        error.response.data ||
        "Server error occurred";
      return { error: errorMessage };
    } else if (error.request) {
      // Network error
      return {
        error: "Network error. Please check your connection and try again.",
      };
    } else {
      // Other errors (validation, etc.)
      return { error: error.message || "An unexpected error occurred" };
    }
  }
};

export const UseCoupon = async (coupon: PostUseCoupon) => {
  const data = {
    SerialKey: coupon.SerialKey,
    Skill_QualityOfWork: coupon.Skill_QualityOfWork,
    Skill_CostEffectiveness: coupon.Skill_CostEffectiveness,
    Skill_Timeliness: coupon.Skill_Timeliness,
    Skill_Communication: coupon.Skill_Communication,
    Skill_Agility: coupon.Skill_Agility,
  };
  try {
    var authToken = getAuthToken();
    const response = await api.post(`${BASE_URL}/Coupons/UseCoupon`, data, {
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
      },
    });

    return response;
  } catch (error) {
    console.error("Error using coupon:", error);
    if (error.response && error.response.data) {
      if (typeof error.response.data === "string") {
        return { error: error.response.data };
      }
      if (error.response.data.error) {
        return { error: error.response.data.error };
      }
    }
    // Provide user-friendly error messages based on status codes
    if (error.response) {
      switch (error.response.status) {
        case 400:
          return {
            error:
              "Invalid coupon data or coupon already used. Please check your information.",
          };
        case 401:
          return { error: "Authentication failed. Please log in again." };
        case 404:
          return {
            error:
              "Coupon not found. Please check the serial key and try again.",
          };
        case 500:
          return {
            error: "Server error while using coupon. Please try again later.",
          };
        default:
          return { error: "Failed to use coupon. Please try again." };
      }
    }
    return {
      error:
        "Network error. Please check your internet connection and try again.",
    };
  }
};

export const DeleteCoupon = async (Id: number) => {
  try {
    var authToken = getAuthToken();
    const response = await api.post(`${BASE_URL}/Coupons/DeleteCoupon/${Id}`, {
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
      },
    });

    return response;
  } catch (error) {
    console.error("Error:", error.message);
  }
};

export function getAuthToken() {
  const cookies = document.cookie.split(";");

  for (let i = 0; i < cookies.length; i++) {
    const cookie = cookies[i].trim();
    if (cookie.startsWith("authToken=")) {
      return cookie.substring("authToken=".length, cookie.length);
    }
  }

  return null;
}
