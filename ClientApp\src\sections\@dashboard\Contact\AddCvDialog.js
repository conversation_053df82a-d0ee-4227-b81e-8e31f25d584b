import { useState, useEffect, useRef } from "react";
import { Worker, Viewer } from "@react-pdf-viewer/core";
import "@react-pdf-viewer/core/lib/styles/index.css";
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Dialog,
  Box,
  DialogContent,
  DialogTitle,
  CircularProgress,
  IconButton,
} from "@mui/material";
import { ToastContainer, toast } from "react-toastify";
import CloseIcon from "@mui/icons-material/Close";
import { CreateContact, EditContact } from "../../../ContactData.ts";
import { FileSelector } from "../../auth/signup/PhotoSelector";
import { useProfile } from "../../../Context/ProfileContext";
import PortraitIcon from "@mui/icons-material/Portrait";

const AddCvDialog = () => {
  const { profile, fetchProfile } = useProfile();
  const [cvContact, setCvContact] = useState(null);
  const [editedContact, setEditedContact] = useState({
    id: 0,
    contactInfo: "",
    isPublic: true,
  });
  const [isCvFileFound, setIsCVFileFound] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isUploading, setIsUploading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const fileURLRef = useRef(null);

  useEffect(() => {
    const existingCvContact = profile.contacts.find(
      (contact) => contact.category === "CvFile"
    );

    if (existingCvContact) {
      setCvContact(existingCvContact);
      setEditedContact(existingCvContact);
      setIsCVFileFound(true);
      fileURLRef.current = existingCvContact.contactInfo;
    }
    setIsLoading(false);
  }, [profile.contacts]);

  const handleFileEdit = async (fileDataUrl) => {
    setIsUploading(true);

    try {
      setEditedContact((prevContact) => ({
        ...prevContact,
        contactInfo: fileDataUrl,
      }));

      if (cvContact) {
        const updatedContact = {
          Id: cvContact.id,
          Category: cvContact.category,
          ContactInfo: fileDataUrl,
          Title: cvContact.title,
          isPublic: cvContact.isPublic,
        };

        const response = await EditContact(updatedContact);
        if (response && response.status === 200) {
          toast.success("CV updated successfully", {
            position: "top-center",
            autoClose: 1000,
          });
          fetchProfile();
          fileURLRef.current = fileDataUrl;
        } else {
          const errorMessage =
            response?.data?.error ||
            response?.data?.message ||
            "Error updating CV";
          toast.error(errorMessage, {
            position: "top-center",
            autoClose: 3000,
          });
        }
      } else {
        const newContact = {
          ContactInfo: fileDataUrl,
          Category: "CvFile",
          isPublic: true,
          UserId: profile.id,
        };

        console.log("Creating CV contact with data:", newContact);
        const response = await CreateContact(newContact);
        if (response) {
          toast.success("CV added successfully", {
            position: "top-center",
            autoClose: 1000,
          });
          fetchProfile();
          fileURLRef.current = fileDataUrl;
        } else {
          toast.error("Error adding CV", {
            position: "top-center",
            autoClose: 1000,
          });
        }
      }
    } catch (error) {
      console.error("Error in handleFileEdit:", error);
      let errorMessage = "Error processing CV file";

      if (error.response) {
        errorMessage =
          error.response.data?.error ||
          error.response.data?.message ||
          `Server error: ${error.response.status}`;
      } else if (error.request) {
        errorMessage = "Network error: Unable to connect to server";
      } else {
        errorMessage = error.message || "Unknown error occurred";
      }

      toast.error(errorMessage, {
        position: "top-center",
        autoClose: 3000,
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleDialogOpen = () => {
    setDialogOpen(true);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
  };

  return (
    <Grid item xs={12} md={12}>
      <Card
        sx={{
          display: "flex",
          flexDirection: "column",
          marginTop: "20px",
        }}
      >
        <Box
          sx={{
            height: "30vh",
            width: "100%",
            display: { xs: "none", sm: "block" },
            overflow: "hidden",
          }}
        >
          <img
            src="../assets/images/Cv.png"
            style={{
              width: "100%",
              height: "100%",
              objectFit: "cover",
            }}
          />
        </Box>
        <CardContent
          sx={{
            display: "flex",
            flexDirection: "column",
            flexGrow: 1,
          }}
        >
          <Typography gutterBottom variant="h5">
            Boost Your Networking with a Professional CV
          </Typography>
          <Typography variant="body2" color="textSecondary" paragraph>
            Upload your CV and enhance your online presence. Share your
            experiences, showcase your skills, and maximize opportunities.
          </Typography>
          <Typography
            variant="caption"
            color="textSecondary"
            sx={{ marginBottom: "20px", display: "block" }}
          >
            Accepted formats: PDF (Max size: 2MB)
          </Typography>
          {/* Push button box to the bottom */}
          <Box
            sx={{
              marginTop: "auto", // Push this box to the bottom
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <FileSelector onSelect={handleFileEdit} isLoading={isUploading} />
            {isCvFileFound && (
              <Button
                variant="contained"
                color="primary"
                onClick={() => setDialogOpen(true)}
                sx={{ borderRadius: "8px" }}
              >
                <span
                  style={{
                    marginRight: "10px",
                  }}
                >
                  Show
                </span>
                <PortraitIcon />
              </Button>
            )}
          </Box>
        </CardContent>
      </Card>
      {/* Dialog for CV */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        fullWidth
        maxWidth="md"
      >
        <DialogTitle>
          CV Preview <PortraitIcon />
        </DialogTitle>
        <DialogContent>
          <IconButton
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
            }}
            aria-label="close"
            onClick={() => setDialogOpen(false)}
          >
            <CloseIcon />
          </IconButton>
          {isLoading ? (
            <CircularProgress />
          ) : (
            <div
              style={{
                height: "600px",
                width: "100%",
                overflow: "auto",
              }}
            >
              <Worker
                workerUrl={`https://unpkg.com/pdfjs-dist@2.15.349/build/pdf.worker.min.js`}
              >
                <Viewer
                  fileUrl={fileURLRef.current}
                  showPreviousViewOnLoad={false}
                />
              </Worker>
            </div>
          )}
        </DialogContent>
      </Dialog>
      <ToastContainer />
    </Grid>
  );
};

export default AddCvDialog;
