import { Helmet } from "react-helmet-async";
import { useNavigate } from "react-router-dom";
import { DataGrid } from "@mui/x-data-grid";
import {
  Button,
  Grid,
  Box,
  Stack,
  Container,
  Tabs,
  Tab,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Avatar,
  IconButton,
  Card,
  CardHeader,
  CardContent,
  CircularProgress,
  useTheme,
  TextField,
} from "@mui/material";
import SimpleBar from "simplebar-react";
import { PurchasingCoupon, GetCoupons } from "../CouponsData.ts";
import { useProfile } from "../Context/ProfileContext";
import { useEffect, useState, lazy, startTransition, Suspense } from "react";
import { useBudget } from "../Context/BudgetContext";
import { styled } from "@mui/system";
import { toast } from "react-toastify";
import { motion } from "framer-motion";
import { fDateTime } from "src/utils/formatTime";

const RemoveCircleOutlineIcon = lazy(() =>
  import("@mui/icons-material/RemoveCircleOutline")
);
const AddCircleOutlineIcon = lazy(() =>
  import("@mui/icons-material/AddCircleOutline")
);
const DeleteIcon = lazy(() => import("@mui/icons-material/Delete"));

// --------------------------------------------------------------

const VerticalLinearStepper = lazy(() =>
  import("../sections/@dashboard/Rating/VerticalLinearStepper.js")
);
const ToastContainer = lazy(() =>
  import("react-toastify").then((module) => ({
    default: module.ToastContainer,
  }))
);

const EmptyContent = lazy(() =>
  import("../sections/@dashboard/Coupons/EmptyContent.js")
);
const InviterFriends = lazy(() =>
  import("../sections/@dashboard/app/InviterFriends.js")
);
const BookingCustomerReviews = lazy(() =>
  import("../sections/@dashboard/Rating/BookingCustomerReviews.js")
);

const steps = [
  {
    label: "Get a coupon",
    description: `Before rating someone's skills, make sure to obtain a coupon. Coupons are essential for rating and evaluating someone's skills. You can either purchase a coupon or look for one in your saved cases.`,
  },
  {
    label: "Find the profile",
    description:
      "Once you have your coupon, proceed to find the profile of the person you want to rate. Take your time to explore their profile thoroughly.",
  },
  {
    label: "Rate their skills",
    description: `Now that you have the coupon and have reviewed the profile, it's time to rate their skills. Provide a rating of 1 to 5 for each skill category. Your feedback helps improve user satisfaction and fosters growth on the platform.`,
  },
];
const AvailableTableColumns = [
  { field: "serialKey", headerName: "Serial Key", width: 240 },
  { field: "reference", headerName: "Reference", width: 140 },
];
const ReservedTableColumns = [
  {
    field: "customerDetails",
    headerName: "Customer",
    width: 250,
    renderCell: (params) => (
      <Stack
        direction="row"
        alignItems="center"
        spacing={2}
        onClick={() => {
          window.open(`/Profile/${params.row.customerUserName}`, "_blank");
        }}
      >
        <Avatar
          alt={params.row.customerFirstName}
          src={params.row.customerProfilePicture}
        />
        <div>
          <Typography variant="subtitle2">
            {params.row.customerFirstName} {params.row.customerLastName}
          </Typography>
          <Typography
            variant="caption"
            sx={{
              color: "text.secondary",
              mt: 0.5,
              display: "block",
            }}
          >
            {params.row.customerUserName}
          </Typography>
        </div>
      </Stack>
    ),
  },
  { field: "reserveDate", headerName: "Date of Reservation", width: 180 },
  { field: "serialKey", headerName: "Serial Key", width: 140 },
];
const UsedTableColumns = [
  {
    field: "customerDetails",
    headerName: "Customer",
    width: 250,
    renderCell: (params) => (
      <Stack
        direction="row"
        alignItems="center"
        spacing={2}
        onClick={() => {
          window.open(`/Profile/${params.row.customerUserName}`, "_blank");
        }}
      >
        <Avatar
          alt={params.row.customerFirstName}
          src={params.row.customerProfilePicture}
        />
        <div>
          <Typography variant="subtitle2">
            {params.row.customerFirstName} {params.row.customerLastName}
          </Typography>
          <Typography
            variant="caption"
            sx={{
              color: "text.secondary",
              mt: 0.5,
              display: "block",
            }}
          >
            {params.row.customerUserName}
          </Typography>
        </div>
      </Stack>
    ),
  },
  { field: "useDate", headerName: "Date of Use", width: 180 },
  { field: "serialKey", headerName: "Serial Key", width: 140 },
];
const Scrollbar = styled(SimpleBar)``;

export const RatingPage = () => {
  const { profile, fetchProfile } = useProfile();
  const { setBudget } = useBudget();
  const [isLoading, setIsLoading] = useState(true);
  const [User, setUser] = useState({
    id: 0,
    email: "",
    firstName: "",
    lastName: "",
    category: "",
    budget: 0.0,
  });
  const [Profile, setProfile] = useState({
    id: 0,
    userId: 0,
    userName: "",
    birthDate: "",
    gender: "",
    profilePicture: "",
    profileCoverPicture: "",
    profilePictureFrame: 0,
    occupation: "",
    isPremium: false,
    user: null,
    socialLinks: null,
    customLinks: null,
    premium: null,
    isSearch: null,
    country: null,
  });
  const [AvailableCoupons, setAvailableCoupons] = useState([]);
  const [ReservedCoupons, setReservedCoupons] = useState([]);
  const [UsedCoupons, setUsedCoupons] = useState([]);
  const [showAvailableCoupons, setShowAvailableCoupons] = useState(false);
  const [showReservedCoupons, setShowReservedCoupons] = useState(false);
  const [showUsedCoupons, setShowUsedCoupons] = useState(false);

  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [PurchaseCoupon, setPurchaseCoupon] = useState({
    title: "coupon",
    quantity: 1,
    available: 99,
    amount: 15,
  });
  const [showGetCoupons, setShowGetCoupons] = useState(false);

  const [activeTab, setActiveTab] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [page, setPage] = useState(0);

  const theme = useTheme();

  const [AvailablePagination, setAvailablePagination] = useState({
    page: 0,
    pageSize: 5,
  });

  const [ReservedPagination, setReservedPagination] = useState({
    page: 0,
    pageSize: 5,
  });

  const [UsedPagination, setUsedPagination] = useState({
    page: 0,
    pageSize: 5,
  });

  const navigate = useNavigate();

  useEffect(() => {
    startTransition(async () => {
      setIsLoading(true);
      await Promise.all([fetchUserData(), fetchCoupons()]);
      setIsLoading(false);
    });
  }, [profile]);

  const fetchUserData = async () => {
    try {
      setUser({
        id: profile.id,
        email: profile.email,
        firstName: profile.firstName,
        lastName: profile.lastName,
        category: profile.category,
      });
      setProfile(profile.profile);
    } catch (error) {
      if (error.redirectToLogin) {
        navigate("/Login");
      }
      console.error("Error fetching profile data:", error);
    }
  };

  const fetchCoupons = async () => {
    try {
      const response = await GetCoupons();
      const usedCoupons = response.filter(
        (coupon) => coupon.isReserved && coupon.isUsed
      );
      const reservedCoupons = response.filter(
        (coupon) => coupon.isReserved && !coupon.isUsed
      );
      const availableCoupons = response.filter(
        (coupon) => !coupon.isReserved && !coupon.isUsed
      );

      if (availableCoupons.length > 0) {
        setAvailableCoupons(availableCoupons);
        setShowAvailableCoupons(true);
      }
      if (reservedCoupons.length > 0) {
        setReservedCoupons(
          reservedCoupons.map((coupon) => {
            coupon.reserveDate = fDateTime(coupon.reserveDate);
            return coupon;
          })
        );
        setShowReservedCoupons(true);
      }
      if (usedCoupons.length > 0) {
        setUsedCoupons(
          usedCoupons.map((coupon) => {
            coupon.useDate = fDateTime(coupon.useDate);
            return coupon;
          })
        );
        setShowUsedCoupons(true);
      }
    } catch (error) {
      console.error("Error with fetching coupons : ", error);
    }
  };

  const handleAddQuantity = (id) => {
    startTransition(() => {
      setPurchaseCoupon((coupon) => ({
        ...coupon,
        quantity: coupon.quantity + 1,
        available: coupon.available - 1,
      }));
    });
  };

  const handleSubtractQuantity = (id) => {
    startTransition(() => {
      setPurchaseCoupon((coupon) => ({
        ...coupon,
        quantity: Math.max(0, coupon.quantity - 1),
        available: coupon.available + 1,
      }));
    });
  };

  const handleQuantityChange = (event) => {
    const newQuantity = parseInt(event.target.value) || 0;
    const maxQuantity = PurchaseCoupon.available + PurchaseCoupon.quantity;
    const validQuantity = Math.max(0, Math.min(newQuantity, maxQuantity));

    startTransition(() => {
      setPurchaseCoupon((coupon) => ({
        ...coupon,
        quantity: validQuantity,
        available: maxQuantity - validQuantity,
      }));
    });
  };

  const handleConfirm = async () => {
    try {
      const response = await PurchasingCoupon({
        UserId: User.id,
        Date: new Date(),
        Country: Profile.country,
        Amount: PurchaseCoupon.amount,
        Number: PurchaseCoupon.quantity,
      });

      setPurchaseCoupon((coupon) => ({
        ...coupon,
        quantity: 0,
      }));

      setShowGetCoupons(false);

      fetchCoupons();

      setBudget(response.data.newAmount);
      startTransition(() => {
        toast.success("Successfully purchased a coupon", {
          position: "top-center",
          autoClose: 1000,
        });
      });
    } catch (error) {
      console.error("Error with purchasing coupons : ", error);
      let errorMessage = "Failed to purchase coupon. Please try again.";

      if (error.response && error.response.data) {
        if (typeof error.response.data === "string") {
          errorMessage = error.response.data;
        } else if (error.response.data.error) {
          errorMessage = error.response.data.error;
        } else if (error.response.data.message) {
          errorMessage = error.response.data.message;
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error(errorMessage, {
        position: "top-center",
        autoClose: 3000,
      });
    }
  };

  const handleCopy = (text) => {
    if (navigator.clipboard) {
      navigator.clipboard
        .writeText(text)
        .then(() => {
          toast.info("Serial Key copied", {
            position: "top-center",
            autoClose: 300,
          });
        })
        .catch((error) => {
          console.error("Failed to copy text: ", error);
          copyToClipboardFallback(text);
        });
    } else {
      copyToClipboardFallback(text);
    }
  };

  const copyToClipboardFallback = (text) => {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand("copy");
    document.body.removeChild(textArea);
    toast.info("Serial Key copied (fallback)", {
      position: "top-center",
      autoClose: 300,
    });
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  return (
    <Container>
      <Helmet>
        <title> IDigics | Rating </title>
      </Helmet>
      {isLoading ? (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            duration: 0.5,
            ease: "easeOut",
          }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              minHeight: "400px",
              flexDirection: "column",
              gap: 2,
            }}
          >
            <CircularProgress size={60} />
            <Typography variant="h6" color="textSecondary">
              Loading rating data...
            </Typography>
          </Box>
        </motion.div>
      ) : (
        <>
          <Typography variant="h5" sx={{ mb: 4 }}>
            Rating
          </Typography>
          <Grid container padding={1} spacing={2}>
            <Grid item xs={12} md={8}>
              <Card>
                <CardHeader
                  title="Exclusive Discount Coupons!"
                  subheader="Interested in saving big on your next purchase? Check out our special coupons!"
                />
                <CardContent>
                  {showGetCoupons ? (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5 }}
                    >
                      {/* Compact Purchase Section */}
                      <Box
                        sx={{
                          border: `2px solid ${theme.palette.primary.main}`,
                          borderRadius: 2,
                          p: 3,
                          mb: 3,
                          backgroundColor: theme.palette.grey[50],
                        }}
                      >
                        <Grid container spacing={3} alignItems="center">
                          {/* Price Info */}
                          <Grid item xs={12} sm={4}>
                            <Box sx={{ textAlign: "center" }}>
                              <Typography
                                variant="body2"
                                color="text.secondary"
                                sx={{ mb: 1 }}
                              >
                                Price per coupon
                              </Typography>
                              <Typography
                                variant="h4"
                                sx={{
                                  color: theme.palette.primary.main,
                                  fontWeight: 700,
                                }}
                              >
                                {PurchaseCoupon.amount} DT
                              </Typography>
                              <Typography
                                variant="caption"
                                color="text.secondary"
                              >
                                {PurchaseCoupon.available} available
                              </Typography>
                            </Box>
                          </Grid>

                          {/* Quantity Controls */}
                          <Grid item xs={12} sm={4}>
                            <Box sx={{ textAlign: "center" }}>
                              <Typography
                                variant="body2"
                                color="text.secondary"
                                sx={{ mb: 2 }}
                              >
                                Quantity
                              </Typography>
                              <Box
                                sx={{
                                  display: "flex",
                                  alignItems: "center",
                                  justifyContent: "center",
                                  gap: 1,
                                }}
                              >
                                <IconButton
                                  size="small"
                                  onClick={() =>
                                    handleSubtractQuantity(PurchaseCoupon.id)
                                  }
                                  disabled={PurchaseCoupon.quantity <= 1}
                                  sx={{
                                    border: `1px solid ${theme.palette.grey[300]}`,
                                    color:
                                      PurchaseCoupon.quantity <= 1
                                        ? theme.palette.grey[400]
                                        : theme.palette.error.main,
                                    "&:hover": {
                                      backgroundColor:
                                        PurchaseCoupon.quantity <= 1
                                          ? "transparent"
                                          : theme.palette.error.lighter,
                                    },
                                  }}
                                >
                                  <Suspense
                                    fallback={<CircularProgress size={16} />}
                                  >
                                    <RemoveCircleOutlineIcon />
                                  </Suspense>
                                </IconButton>

                                <TextField
                                  type="number"
                                  value={PurchaseCoupon.quantity}
                                  onChange={handleQuantityChange}
                                  inputProps={{
                                    min: 1,
                                    max:
                                      PurchaseCoupon.available +
                                      PurchaseCoupon.quantity,
                                    style: { textAlign: "center" },
                                  }}
                                  sx={{
                                    minWidth: "60px",
                                    maxWidth: "80px",
                                    mx: 1,
                                    "& .MuiOutlinedInput-root": {
                                      height: "40px",
                                      fontSize: "1.2rem",
                                      fontWeight: 600,
                                    },
                                    "& .MuiOutlinedInput-input": {
                                      padding: "8px 12px",
                                      textAlign: "center",
                                    },
                                  }}
                                  size="small"
                                />

                                <IconButton
                                  size="small"
                                  onClick={() =>
                                    handleAddQuantity(PurchaseCoupon.id)
                                  }
                                  disabled={
                                    PurchaseCoupon.quantity >=
                                    PurchaseCoupon.available
                                  }
                                  sx={{
                                    border: `1px solid ${theme.palette.grey[300]}`,
                                    color:
                                      PurchaseCoupon.quantity >=
                                      PurchaseCoupon.available
                                        ? theme.palette.grey[400]
                                        : theme.palette.success.main,
                                    "&:hover": {
                                      backgroundColor:
                                        PurchaseCoupon.quantity >=
                                        PurchaseCoupon.available
                                          ? "transparent"
                                          : theme.palette.success.lighter,
                                    },
                                  }}
                                >
                                  <Suspense
                                    fallback={<CircularProgress size={16} />}
                                  >
                                    <AddCircleOutlineIcon />
                                  </Suspense>
                                </IconButton>
                              </Box>
                            </Box>
                          </Grid>

                          {/* Total and Actions */}
                          <Grid item xs={12} sm={4}>
                            <Box sx={{ textAlign: "center" }}>
                              <Typography
                                variant="body2"
                                color="text.secondary"
                                sx={{ mb: 1 }}
                              >
                                Total
                              </Typography>
                              <Typography
                                variant="h4"
                                sx={{
                                  color: theme.palette.success.main,
                                  fontWeight: 700,
                                  mb: 2,
                                }}
                              >
                                {PurchaseCoupon.amount *
                                  PurchaseCoupon.quantity}{" "}
                                DT
                              </Typography>
                              <Stack
                                direction="row"
                                spacing={1}
                                justifyContent="center"
                              >
                                <Button
                                  variant="outlined"
                                  size="small"
                                  onClick={() => setConfirmDialogOpen(true)}
                                  sx={{ minWidth: "auto", px: 1 }}
                                >
                                  Cancel
                                </Button>
                                <Button
                                  variant="contained"
                                  onClick={handleConfirm}
                                  sx={{
                                    backgroundColor: theme.palette.primary.main,
                                    "&:hover": {
                                      backgroundColor:
                                        theme.palette.primary.dark,
                                    },
                                  }}
                                >
                                  Purchase
                                </Button>
                              </Stack>
                            </Box>
                          </Grid>
                        </Grid>
                      </Box>
                    </motion.div>
                  ) : showAvailableCoupons ? (
                    <>
                      <Button
                        variant="contained"
                        color="primary"
                        sx={{ marginBottom: "40px" }}
                        onClick={() => {
                          setShowGetCoupons(true);
                        }}
                      >
                        Get Coupons
                      </Button>
                      <Suspense fallback={<CircularProgress />}>
                        <Scrollbar>
                          <Box sx={{ height: 370 }}>
                            <DataGrid
                              columns={AvailableTableColumns}
                              rows={AvailableCoupons}
                              onRowClick={(o) => handleCopy(o.row.serialKey)}
                              sx={{
                                "& .MuiDataGrid-cell:focus": {
                                  outline: "none",
                                },
                                "& .MuiDataGrid-cell": {
                                  alignContent: "center",
                                },
                                border: "none",
                                backgroundColor: theme.palette.common.white,
                              }}
                              paginationModel={AvailablePagination}
                              paginationMode="client"
                              onPaginationModelChange={setAvailablePagination}
                            />
                          </Box>
                        </Scrollbar>
                      </Suspense>
                    </>
                  ) : (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5 }}
                    >
                      <Box sx={{ textAlign: "center", py: 3 }}>
                        <Button
                          variant="contained"
                          size="large"
                          onClick={() => {
                            setShowGetCoupons(true);
                          }}
                          sx={{
                            backgroundColor: theme.palette.primary.main,
                            "&:hover": {
                              backgroundColor: theme.palette.primary.dark,
                              transform: "translateY(-2px)",
                              boxShadow: theme.customShadows.primary,
                            },
                            transition: "all 0.3s ease",
                            px: 4,
                            py: 1.5,
                            fontSize: "1rem",
                            fontWeight: 600,
                            mb: 3,
                          }}
                        >
                          Get Coupons
                        </Button>

                        <Suspense fallback={<CircularProgress />}>
                          <EmptyContent
                            title="You don't have any coupons"
                            description="Looks like you have no items in your shopping Coupons."
                            img="/assets/illustrations/illustration_empty_cart.svg"
                          />
                        </Suspense>
                      </Box>
                    </motion.div>
                  )}
                </CardContent>
              </Card>

              <Card sx={{ mt: 3 }}>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    gap: "10px",
                    padding: "10px",
                  }}
                >
                  <Tabs
                    value={activeTab}
                    onChange={handleTabChange}
                    aria-label="Account tabs"
                  >
                    <Tab label="Reserved" />
                    <Tab label="Used" />
                  </Tabs>
                </Box>
                <CardContent>
                  {activeTab === 0 ? (
                    <Suspense fallback={<CircularProgress />}>
                      <Scrollbar>
                        <Box sx={{ height: 370 }}>
                          <DataGrid
                            columns={ReservedTableColumns}
                            rows={ReservedCoupons}
                            sx={{
                              "& .MuiDataGrid-cell:focus": {
                                outline: "none",
                              },
                              "& .MuiDataGrid-cell": {
                                alignContent: "center",
                              },
                              border: "none",
                              backgroundColor: theme.palette.common.white,
                            }}
                            paginationModel={ReservedPagination}
                            paginationMode="client"
                            onPaginationModelChange={setReservedPagination}
                          />
                        </Box>
                      </Scrollbar>
                    </Suspense>
                  ) : (
                    <Suspense fallback={<CircularProgress />}>
                      <Scrollbar>
                        <Box sx={{ height: 370 }}>
                          <DataGrid
                            columns={UsedTableColumns}
                            rows={UsedCoupons}
                            sx={{
                              "& .MuiDataGrid-cell:focus": {
                                outline: "none",
                              },
                              "& .MuiDataGrid-cell": {
                                alignContent: "center",
                              },
                              border: "none",
                              backgroundColor: theme.palette.common.white,
                            }}
                            paginationModel={UsedPagination}
                            paginationMode="client"
                            onPaginationModelChange={setUsedPagination}
                          />
                        </Box>
                      </Scrollbar>
                    </Suspense>
                  )}
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={4}>
              <Stack spacing={3}>
                {/* <Suspense fallback={<CircularProgress />}>
                            <BookingCustomerReviews />
                        </Suspense> */}

                <Card>
                  <CardContent>
                    <Suspense fallback={<CircularProgress />}>
                      <VerticalLinearStepper steps={steps} />
                    </Suspense>
                  </CardContent>
                </Card>
                <Suspense fallback={<CircularProgress />}>
                  <InviterFriends />
                </Suspense>
              </Stack>
            </Grid>
          </Grid>
          <Dialog
            open={confirmDialogOpen}
            onClose={() => {
              setConfirmDialogOpen(false);
            }}
            aria-labelledby="confirm-dialog-title"
            aria-describedby="confirm-dialog-description"
          >
            <DialogTitle id="confirm-dialog-title">
              Confirm Deletion
            </DialogTitle>
            <DialogContent>
              <DialogContentText id="confirm-dialog-description">
                Are you sure you want to delete this item?
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button
                onClick={() => setConfirmDialogOpen(false)}
                color="primary"
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  setPurchaseCoupon((coupon) => ({
                    ...coupon,
                    quantity: 0,
                  }));
                  setConfirmDialogOpen(false);
                  setShowGetCoupons(false);
                }}
                color="primary"
                autoFocus
              >
                Confirm
              </Button>
            </DialogActions>
          </Dialog>
          <Suspense fallback={<CircularProgress />}>
            <ToastContainer />
          </Suspense>
        </>
      )}
    </Container>
  );
};
