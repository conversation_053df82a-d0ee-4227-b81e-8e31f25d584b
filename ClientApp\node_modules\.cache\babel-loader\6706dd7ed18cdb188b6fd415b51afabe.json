{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\iDigitalX\\\\ClientApp\\\\src\\\\pages\\\\AnalyticsPage.js\",\n  _s = $RefreshSig$();\nimport { Helmet } from \"react-helmet-async\";\nimport { useTheme } from \"@mui/material/styles\";\nimport { useEffect, useState, lazy, Suspense, startTransition } from \"react\";\nimport { GetClicks, GetViews, calculateDailyClicks, calculateCountryClicks, calculateGenderClicks, calculateDailyViews, GetCategoryClickCounts, getClickCountsByCategory } from \"../AnalyticsData.ts\";\nimport { Container, Grid, Typography, CircularProgress, Box } from \"@mui/material\";\nimport { motion } from \"framer-motion\";\n\n// Lazy load components\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppWidgetSummary = /*#__PURE__*/lazy(_c = () => import(\"../sections/@dashboard/app/AppWidgetSummary\"));\n_c2 = AppWidgetSummary;\nconst AppConversionRates = /*#__PURE__*/lazy(_c3 = () => import(\"../sections/@dashboard/app/AppConversionRates\"));\n_c4 = AppConversionRates;\nconst AppCurrentVisits = /*#__PURE__*/lazy(_c5 = () => import(\"../sections/@dashboard/app/AppCurrentVisits\"));\n_c6 = AppCurrentVisits;\nconst AppWebsiteVisits = /*#__PURE__*/lazy(_c7 = () => import(\"../sections/@dashboard/app/AppWebsiteVisits\"));\n_c8 = AppWebsiteVisits;\nconst EmptyContent = /*#__PURE__*/lazy(_c9 = () => import(\"../sections/@dashboard/Coupons/EmptyContent.js\"));\n_c10 = EmptyContent;\nconst AppTasks = /*#__PURE__*/lazy(_c11 = () => import(\"../sections/@dashboard/app/AppTasks\"));\n_c12 = AppTasks;\nexport default function AnalyticsPage() {\n  _s();\n  const theme = useTheme();\n  const [isLoading, setIsLoading] = useState(true);\n  const [Clicks, setClicks] = useState([]);\n  const [Views, setViews] = useState([]);\n  const [Daily, setDaily] = useState([]);\n  const [CategoryClicks, setCategoryClicks] = useState([]);\n  const [Country, setCountry] = useState([]);\n  const [Gender, setGender] = useState([]);\n  const [LinksTotalCount, setLinksTotalCount] = useState([]);\n  const [DailyViews, setDailyViews] = useState([]);\n  useEffect(() => {\n    startTransition(async () => {\n      setIsLoading(true);\n      try {\n        await Promise.all([fetchClicksData(), fetchViewsData()]);\n      } catch (error) {\n        console.error(\"Error fetching analytics data:\", error);\n      } finally {\n        setIsLoading(false);\n      }\n    });\n  }, []);\n  const fetchClicksData = async () => {\n    try {\n      const response = await GetClicks();\n      if (response && response.data) {\n        setClicks(response.data);\n        setDaily(calculateDailyClicks(response.data));\n        setCountry(calculateCountryClicks(response.data));\n        setGender(calculateGenderClicks(response.data));\n        setCategoryClicks(getClickCountsByCategory(response.data));\n        setLinksTotalCount(GetCategoryClickCounts(response.data));\n      } else {\n        console.warn(\"No click data received\");\n        setClicks([]);\n      }\n    } catch (error) {\n      console.error(\"Error fetching Clicks data:\", error);\n      setClicks([]);\n      // Don't show error toast for analytics - just log it\n    }\n  };\n  const fetchViewsData = async () => {\n    try {\n      const response = await GetViews();\n      if (response && response.data) {\n        setViews(response.data);\n        setDailyViews(calculateDailyViews(response.data));\n      } else {\n        console.warn(\"No view data received\");\n        setViews([]);\n      }\n    } catch (error) {\n      console.error(\"Error fetching Views data:\", error);\n      setViews([]);\n      // Don't show error toast for analytics - just log it\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: /*#__PURE__*/_jsxDEV(\"title\", {\n        children: \" IDigics | Analytics \"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      sx: {\n        mb: 4\n      },\n      children: \"Analytics\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Suspense, {\n      fallback: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 27\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        maxWidth: \"xl\",\n        children: isLoading ? /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.5,\n            ease: \"easeOut\"\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"flex\",\n              justifyContent: \"center\",\n              alignItems: \"center\",\n              minHeight: \"400px\",\n              flexDirection: \"column\",\n              gap: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 60\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              color: \"textSecondary\",\n              children: \"Loading analytics data...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 13\n        }, this) : Views.length == 0 || Clicks.length == 0 ? /*#__PURE__*/_jsxDEV(EmptyContent, {\n          title: \"No Analytics Data Available\",\n          description: \"It looks like there hasn't been any traffic yet. As soon as you start getting clicks and views, your analytics will populate here.\",\n          img: \"/assets/illustrations/illustration_empty_cart.svg\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Suspense, {\n              fallback: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 37\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(AppWidgetSummary, {\n                title: \"Total CLicks\",\n                total: Clicks.reduce((total, click) => total + click.clickCount, 0),\n                color: \"info\",\n                icon: \"ant-design:edit-filled\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Suspense, {\n              fallback: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 37\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(AppWidgetSummary, {\n                title: \"Total Views\",\n                total: Views.reduce((total, view) => total + view.viewsCount, 0),\n                color: \"error\",\n                icon: \"ant-design:eye-filled\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            lg: 8,\n            children: /*#__PURE__*/_jsxDEV(Suspense, {\n              fallback: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 37\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(AppConversionRates, {\n                title: \"Daily Views\",\n                subheader: \"Analyze the daily views to understand the trends and patterns in the number of views your content receives.Gain valuable insights into the most active days and make informed decisions based on this data.\",\n                chartData: DailyViews.map(item => ({\n                  label: item.date,\n                  value: item.totalClicks\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Suspense, {\n              fallback: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 37\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(AppCurrentVisits, {\n                title: \"Countries\",\n                subheader: \"Discover the top countries where your links\",\n                chartData: Country.map(item => ({\n                  label: item.country,\n                  value: item.totalClicks\n                })),\n                chartColors: [theme.palette.success.main, theme.palette.warning.main, theme.palette.text.secondary, theme.palette.info.main, theme.palette.primary.main, theme.palette.error.main]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            lg: 8,\n            children: /*#__PURE__*/_jsxDEV(Suspense, {\n              fallback: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 37\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(AppConversionRates, {\n                title: \"Daily Engagement\",\n                subheader: \"Explore daily engagement metrics to track the interaction and activity of users with your content. Understand when your audience is most engaged, helping you optimize your strategies for higher user interaction and better overall engagement.\",\n                chartData: Daily.map(item => ({\n                  label: item.date,\n                  value: item.totalClicks\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Suspense, {\n              fallback: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 37\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(AppCurrentVisits, {\n                title: \"Genders\",\n                subheader: \"Explore the distribution of link clicks based on gender\",\n                chartData: Gender.map(item => ({\n                  label: item.gender,\n                  value: item.totalClicks\n                })),\n                chartColors: [theme.palette.error.main, theme.palette.secondary.main]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 108,\n    columnNumber: 5\n  }, this);\n}\n_s(AnalyticsPage, \"dcGAIzXJ6ONDwXSdH1BHd9ures0=\", false, function () {\n  return [useTheme];\n});\n_c13 = AnalyticsPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13;\n$RefreshReg$(_c, \"AppWidgetSummary$lazy\");\n$RefreshReg$(_c2, \"AppWidgetSummary\");\n$RefreshReg$(_c3, \"AppConversionRates$lazy\");\n$RefreshReg$(_c4, \"AppConversionRates\");\n$RefreshReg$(_c5, \"AppCurrentVisits$lazy\");\n$RefreshReg$(_c6, \"AppCurrentVisits\");\n$RefreshReg$(_c7, \"AppWebsiteVisits$lazy\");\n$RefreshReg$(_c8, \"AppWebsiteVisits\");\n$RefreshReg$(_c9, \"EmptyContent$lazy\");\n$RefreshReg$(_c10, \"EmptyContent\");\n$RefreshReg$(_c11, \"AppTasks$lazy\");\n$RefreshReg$(_c12, \"AppTasks\");\n$RefreshReg$(_c13, \"AnalyticsPage\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "useTheme", "useEffect", "useState", "lazy", "Suspense", "startTransition", "GetClicks", "GetViews", "calculateDailyClicks", "calculateCountryClicks", "calculateGenderClicks", "calculateDailyViews", "GetCategoryClickCounts", "getClickCountsByCategory", "Container", "Grid", "Typography", "CircularProgress", "Box", "motion", "jsxDEV", "_jsxDEV", "AppWidgetSummary", "_c", "_c2", "AppConversionRates", "_c3", "_c4", "AppCurrentVisits", "_c5", "_c6", "AppWebsiteVisits", "_c7", "_c8", "EmptyContent", "_c9", "_c10", "AppTasks", "_c11", "_c12", "AnalyticsPage", "_s", "theme", "isLoading", "setIsLoading", "<PERSON>licks", "setClicks", "Views", "setViews", "Daily", "<PERSON><PERSON><PERSON><PERSON>", "CategoryClicks", "setCategoryClicks", "Country", "setCountry", "Gender", "setGender", "LinksTotalCount", "setLinksTotalCount", "DailyViews", "setDailyViews", "Promise", "all", "fetchClicksData", "fetchViewsData", "error", "console", "response", "data", "warn", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "sx", "mb", "fallback", "max<PERSON><PERSON><PERSON>", "div", "initial", "opacity", "y", "animate", "transition", "duration", "ease", "display", "justifyContent", "alignItems", "minHeight", "flexDirection", "gap", "size", "color", "length", "title", "description", "img", "container", "spacing", "item", "xs", "sm", "md", "total", "reduce", "click", "clickCount", "icon", "view", "viewsCount", "lg", "subheader", "chartData", "map", "label", "date", "value", "totalClicks", "country", "chartColors", "palette", "success", "main", "warning", "text", "secondary", "info", "primary", "gender", "_c13", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/iDigitalX/ClientApp/src/pages/AnalyticsPage.js"], "sourcesContent": ["import { Helmet } from \"react-helmet-async\";\r\nimport { useTheme } from \"@mui/material/styles\";\r\nimport { useEffect, useState, lazy, Suspense, startTransition } from \"react\";\r\nimport {\r\n  GetClicks,\r\n  GetViews,\r\n  calculateDailyClicks,\r\n  calculateCountryClicks,\r\n  calculateGenderClicks,\r\n  calculateDailyViews,\r\n  GetCategoryClickCounts,\r\n  getClickCountsByCategory,\r\n} from \"../AnalyticsData.ts\";\r\nimport {\r\n  Container,\r\n  Grid,\r\n  Typography,\r\n  CircularProgress,\r\n  Box,\r\n} from \"@mui/material\";\r\nimport { motion } from \"framer-motion\";\r\n\r\n// Lazy load components\r\nconst AppWidgetSummary = lazy(() =>\r\n  import(\"../sections/@dashboard/app/AppWidgetSummary\")\r\n);\r\nconst AppConversionRates = lazy(() =>\r\n  import(\"../sections/@dashboard/app/AppConversionRates\")\r\n);\r\nconst AppCurrentVisits = lazy(() =>\r\n  import(\"../sections/@dashboard/app/AppCurrentVisits\")\r\n);\r\nconst AppWebsiteVisits = lazy(() =>\r\n  import(\"../sections/@dashboard/app/AppWebsiteVisits\")\r\n);\r\n\r\nconst EmptyContent = lazy(() =>\r\n  import(\"../sections/@dashboard/Coupons/EmptyContent.js\")\r\n);\r\n\r\nconst AppTasks = lazy(() => import(\"../sections/@dashboard/app/AppTasks\"));\r\n\r\nexport default function AnalyticsPage() {\r\n  const theme = useTheme();\r\n\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [Clicks, setClicks] = useState([]);\r\n  const [Views, setViews] = useState([]);\r\n  const [Daily, setDaily] = useState([]);\r\n  const [CategoryClicks, setCategoryClicks] = useState([]);\r\n  const [Country, setCountry] = useState([]);\r\n  const [Gender, setGender] = useState([]);\r\n  const [LinksTotalCount, setLinksTotalCount] = useState([]);\r\n  const [DailyViews, setDailyViews] = useState([]);\r\n\r\n  useEffect(() => {\r\n    startTransition(async () => {\r\n      setIsLoading(true);\r\n      try {\r\n        await Promise.all([fetchClicksData(), fetchViewsData()]);\r\n      } catch (error) {\r\n        console.error(\"Error fetching analytics data:\", error);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    });\r\n  }, []);\r\n\r\n  const fetchClicksData = async () => {\r\n    try {\r\n      const response = await GetClicks();\r\n      if (response && response.data) {\r\n        setClicks(response.data);\r\n        setDaily(calculateDailyClicks(response.data));\r\n        setCountry(calculateCountryClicks(response.data));\r\n        setGender(calculateGenderClicks(response.data));\r\n        setCategoryClicks(getClickCountsByCategory(response.data));\r\n        setLinksTotalCount(GetCategoryClickCounts(response.data));\r\n      } else {\r\n        console.warn(\"No click data received\");\r\n        setClicks([]);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching Clicks data:\", error);\r\n      setClicks([]);\r\n      // Don't show error toast for analytics - just log it\r\n    }\r\n  };\r\n\r\n  const fetchViewsData = async () => {\r\n    try {\r\n      const response = await GetViews();\r\n      if (response && response.data) {\r\n        setViews(response.data);\r\n        setDailyViews(calculateDailyViews(response.data));\r\n      } else {\r\n        console.warn(\"No view data received\");\r\n        setViews([]);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching Views data:\", error);\r\n      setViews([]);\r\n      // Don't show error toast for analytics - just log it\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Container>\r\n      <Helmet>\r\n        <title> IDigics | Analytics </title>\r\n      </Helmet>\r\n      <Typography variant=\"h5\" sx={{ mb: 4 }}>\r\n        Analytics\r\n      </Typography>\r\n\r\n      <Suspense fallback={<CircularProgress />}>\r\n        <Container maxWidth=\"xl\">\r\n          {isLoading ? (\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{\r\n                duration: 0.5,\r\n                ease: \"easeOut\",\r\n              }}\r\n            >\r\n              <Box\r\n                sx={{\r\n                  display: \"flex\",\r\n                  justifyContent: \"center\",\r\n                  alignItems: \"center\",\r\n                  minHeight: \"400px\",\r\n                  flexDirection: \"column\",\r\n                  gap: 2,\r\n                }}\r\n              >\r\n                <CircularProgress size={60} />\r\n                <Typography variant=\"h6\" color=\"textSecondary\">\r\n                  Loading analytics data...\r\n                </Typography>\r\n              </Box>\r\n            </motion.div>\r\n          ) : Views.length == 0 || Clicks.length == 0 ? (\r\n            <EmptyContent\r\n              title=\"No Analytics Data Available\"\r\n              description=\"It looks like there hasn't been any traffic yet. As soon as you start getting clicks and views, your analytics will populate here.\"\r\n              img=\"/assets/illustrations/illustration_empty_cart.svg\"\r\n            />\r\n          ) : (\r\n            <Grid container spacing={3}>\r\n              <Grid item xs={12} sm={6} md={6}>\r\n                <Suspense fallback={<CircularProgress />}>\r\n                  <AppWidgetSummary\r\n                    title=\"Total CLicks\"\r\n                    total={Clicks.reduce(\r\n                      (total, click) => total + click.clickCount,\r\n                      0\r\n                    )}\r\n                    color={\"info\"}\r\n                    icon={\"ant-design:edit-filled\"}\r\n                  />\r\n                </Suspense>\r\n              </Grid>\r\n\r\n              <Grid item xs={12} sm={6} md={6}>\r\n                <Suspense fallback={<CircularProgress />}>\r\n                  <AppWidgetSummary\r\n                    title=\"Total Views\"\r\n                    total={Views.reduce(\r\n                      (total, view) => total + view.viewsCount,\r\n                      0\r\n                    )}\r\n                    color=\"error\"\r\n                    icon={\"ant-design:eye-filled\"}\r\n                  />\r\n                </Suspense>\r\n              </Grid>\r\n\r\n              {/* <Grid item xs={12} sm={6} md={4}>\r\n                                <Suspense fallback={<CircularProgress />}>\r\n                                    <AppWidgetSummary\r\n                                        title=\"Most Viewed Country\"\r\n                                        total={\r\n                                            MostClickedCountry(Clicks) || \"-\"\r\n                                        } // Set \"-\" as the default value\r\n                                        color=\"success\"\r\n                                        icon={\"ant-design:flag-filled\"}\r\n                                    />\r\n                                </Suspense>\r\n                            </Grid> */}\r\n\r\n              <Grid item xs={12} md={6} lg={8}>\r\n                <Suspense fallback={<CircularProgress />}>\r\n                  <AppConversionRates\r\n                    title=\"Daily Views\"\r\n                    subheader=\"Analyze the daily views to understand the trends and patterns in the number of views your content receives.Gain valuable insights into the most active days and make informed decisions based on this data.\"\r\n                    chartData={DailyViews.map((item) => ({\r\n                      label: item.date,\r\n                      value: item.totalClicks,\r\n                    }))}\r\n                  />\r\n                </Suspense>\r\n              </Grid>\r\n\r\n              <Grid item xs={12} md={6} lg={4}>\r\n                <Suspense fallback={<CircularProgress />}>\r\n                  <AppCurrentVisits\r\n                    title=\"Countries\"\r\n                    subheader=\"Discover the top countries where your links\"\r\n                    chartData={Country.map((item) => ({\r\n                      label: item.country,\r\n                      value: item.totalClicks,\r\n                    }))}\r\n                    chartColors={[\r\n                      theme.palette.success.main,\r\n                      theme.palette.warning.main,\r\n                      theme.palette.text.secondary,\r\n                      theme.palette.info.main,\r\n                      theme.palette.primary.main,\r\n                      theme.palette.error.main,\r\n                    ]}\r\n                  />\r\n                </Suspense>\r\n              </Grid>\r\n\r\n              <Grid item xs={12} md={6} lg={8}>\r\n                <Suspense fallback={<CircularProgress />}>\r\n                  <AppConversionRates\r\n                    title=\"Daily Engagement\"\r\n                    subheader=\"Explore daily engagement metrics to track the interaction and activity of users with your content. Understand when your audience is most engaged, helping you optimize your strategies for higher user interaction and better overall engagement.\"\r\n                    chartData={Daily.map((item) => ({\r\n                      label: item.date,\r\n                      value: item.totalClicks,\r\n                    }))}\r\n                  />\r\n                </Suspense>\r\n              </Grid>\r\n\r\n              <Grid item xs={12} md={6} lg={4}>\r\n                <Suspense fallback={<CircularProgress />}>\r\n                  <AppCurrentVisits\r\n                    title=\"Genders\"\r\n                    subheader=\"Explore the distribution of link clicks based on gender\"\r\n                    chartData={Gender.map((item) => ({\r\n                      label: item.gender,\r\n                      value: item.totalClicks,\r\n                    }))}\r\n                    chartColors={[\r\n                      theme.palette.error.main,\r\n                      theme.palette.secondary.main,\r\n                    ]}\r\n                  />\r\n                </Suspense>\r\n              </Grid>\r\n\r\n              {/* <Grid item xs={12} md={6} lg={8}>\r\n                            <Suspense fallback={<CircularProgress />}>\r\n                                <AppWebsiteVisits\r\n                                    title=\"Website Visits\"\r\n                                    subheader=\"(+43%) than last year\"\r\n                                    chartLabels={[\r\n                                        \"01/01/2003\",\r\n                                        \"02/01/2003\",\r\n                                        \"03/01/2003\",\r\n                                        \"04/01/2003\",\r\n                                        \"05/01/2003\",\r\n                                        \"06/01/2003\",\r\n                                        \"07/01/2003\",\r\n                                        \"08/01/2003\",\r\n                                        \"09/01/2003\",\r\n                                        \"10/01/2003\",\r\n                                        \"11/01/2003\",\r\n                                    ]}\r\n                                    chartData={[\r\n                                        {\r\n                                            name: \"Team A\",\r\n                                            type: \"column\",\r\n                                            fill: \"solid\",\r\n                                            data: [\r\n                                                23, 11, 22, 27, 13, 22, 37, 21,\r\n                                                44, 22, 30,\r\n                                            ],\r\n                                        },\r\n                                        {\r\n                                            name: \"Team B\",\r\n                                            type: \"area\",\r\n                                            fill: \"gradient\",\r\n                                            data: [\r\n                                                44, 55, 41, 67, 22, 43, 21, 41,\r\n                                                56, 27, 43,\r\n                                            ],\r\n                                        },\r\n                                        {\r\n                                            name: \"Team C\",\r\n                                            type: \"line\",\r\n                                            fill: \"solid\",\r\n                                            data: [\r\n                                                30, 25, 36, 30, 45, 35, 64, 52,\r\n                                                59, 36, 39,\r\n                                            ],\r\n                                        },\r\n                                    ]}\r\n                                />\r\n                            </Suspense>\r\n                        </Grid> */}\r\n\r\n              {/* <Grid item xs={12} md={6} lg={8}>\r\n                            <Suspense fallback={<CircularProgress />}>\r\n                                <AppTasks\r\n                                    title=\"Tasks\"\r\n                                    list={[\r\n                                        {\r\n                                            id: \"1\",\r\n                                            label: \"Create FireStone Logo\",\r\n                                        },\r\n                                        {\r\n                                            id: \"2\",\r\n                                            label: \"Add SCSS and JS files if required\",\r\n                                        },\r\n                                        {\r\n                                            id: \"3\",\r\n                                            label: \"Stakeholder Meeting\",\r\n                                        },\r\n                                        {\r\n                                            id: \"4\",\r\n                                            label: \"Scoping & Estimations\",\r\n                                        },\r\n                                        { id: \"5\", label: \"Sprint Showcase\" },\r\n                                    ]}\r\n                                />\r\n                            </Suspense>\r\n                        </Grid> */}\r\n            </Grid>\r\n          )}\r\n        </Container>\r\n      </Suspense>\r\n    </Container>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,SAASA,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,SAAS,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,eAAe,QAAQ,OAAO;AAC5E,SACEC,SAAS,EACTC,QAAQ,EACRC,oBAAoB,EACpBC,sBAAsB,EACtBC,qBAAqB,EACrBC,mBAAmB,EACnBC,sBAAsB,EACtBC,wBAAwB,QACnB,qBAAqB;AAC5B,SACEC,SAAS,EACTC,IAAI,EACJC,UAAU,EACVC,gBAAgB,EAChBC,GAAG,QACE,eAAe;AACtB,SAASC,MAAM,QAAQ,eAAe;;AAEtC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,gBAAgB,gBAAGnB,IAAI,CAAAoB,EAAA,GAACA,CAAA,KAC5B,MAAM,CAAC,6CAA6C,CACtD,CAAC;AAACC,GAAA,GAFIF,gBAAgB;AAGtB,MAAMG,kBAAkB,gBAAGtB,IAAI,CAAAuB,GAAA,GAACA,CAAA,KAC9B,MAAM,CAAC,+CAA+C,CACxD,CAAC;AAACC,GAAA,GAFIF,kBAAkB;AAGxB,MAAMG,gBAAgB,gBAAGzB,IAAI,CAAA0B,GAAA,GAACA,CAAA,KAC5B,MAAM,CAAC,6CAA6C,CACtD,CAAC;AAACC,GAAA,GAFIF,gBAAgB;AAGtB,MAAMG,gBAAgB,gBAAG5B,IAAI,CAAA6B,GAAA,GAACA,CAAA,KAC5B,MAAM,CAAC,6CAA6C,CACtD,CAAC;AAACC,GAAA,GAFIF,gBAAgB;AAItB,MAAMG,YAAY,gBAAG/B,IAAI,CAAAgC,GAAA,GAACA,CAAA,KACxB,MAAM,CAAC,gDAAgD,CACzD,CAAC;AAACC,IAAA,GAFIF,YAAY;AAIlB,MAAMG,QAAQ,gBAAGlC,IAAI,CAAAmC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC,CAAC;AAACC,IAAA,GAArEF,QAAQ;AAEd,eAAe,SAASG,aAAaA,CAAA,EAAG;EAAAC,EAAA;EACtC,MAAMC,KAAK,GAAG1C,QAAQ,CAAC,CAAC;EAExB,MAAM,CAAC2C,SAAS,EAAEC,YAAY,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC2C,MAAM,EAAEC,SAAS,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC6C,KAAK,EAAEC,QAAQ,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC+C,KAAK,EAAEC,QAAQ,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiD,cAAc,EAAEC,iBAAiB,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACmD,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACqD,MAAM,EAAEC,SAAS,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACuD,eAAe,EAAEC,kBAAkB,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACyD,UAAU,EAAEC,aAAa,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAEhDD,SAAS,CAAC,MAAM;IACdI,eAAe,CAAC,YAAY;MAC1BuC,YAAY,CAAC,IAAI,CAAC;MAClB,IAAI;QACF,MAAMiB,OAAO,CAACC,GAAG,CAAC,CAACC,eAAe,CAAC,CAAC,EAAEC,cAAc,CAAC,CAAC,CAAC,CAAC;MAC1D,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACxD,CAAC,SAAS;QACRrB,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMmB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAM7D,SAAS,CAAC,CAAC;MAClC,IAAI6D,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE;QAC7BtB,SAAS,CAACqB,QAAQ,CAACC,IAAI,CAAC;QACxBlB,QAAQ,CAAC1C,oBAAoB,CAAC2D,QAAQ,CAACC,IAAI,CAAC,CAAC;QAC7Cd,UAAU,CAAC7C,sBAAsB,CAAC0D,QAAQ,CAACC,IAAI,CAAC,CAAC;QACjDZ,SAAS,CAAC9C,qBAAqB,CAACyD,QAAQ,CAACC,IAAI,CAAC,CAAC;QAC/ChB,iBAAiB,CAACvC,wBAAwB,CAACsD,QAAQ,CAACC,IAAI,CAAC,CAAC;QAC1DV,kBAAkB,CAAC9C,sBAAsB,CAACuD,QAAQ,CAACC,IAAI,CAAC,CAAC;MAC3D,CAAC,MAAM;QACLF,OAAO,CAACG,IAAI,CAAC,wBAAwB,CAAC;QACtCvB,SAAS,CAAC,EAAE,CAAC;MACf;IACF,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDnB,SAAS,CAAC,EAAE,CAAC;MACb;IACF;EACF,CAAC;EAED,MAAMkB,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAM5D,QAAQ,CAAC,CAAC;MACjC,IAAI4D,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE;QAC7BpB,QAAQ,CAACmB,QAAQ,CAACC,IAAI,CAAC;QACvBR,aAAa,CAACjD,mBAAmB,CAACwD,QAAQ,CAACC,IAAI,CAAC,CAAC;MACnD,CAAC,MAAM;QACLF,OAAO,CAACG,IAAI,CAAC,uBAAuB,CAAC;QACrCrB,QAAQ,CAAC,EAAE,CAAC;MACd;IACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDjB,QAAQ,CAAC,EAAE,CAAC;MACZ;IACF;EACF,CAAC;EAED,oBACE3B,OAAA,CAACP,SAAS;IAAAwD,QAAA,gBACRjD,OAAA,CAACtB,MAAM;MAAAuE,QAAA,eACLjD,OAAA;QAAAiD,QAAA,EAAO;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eACTrD,OAAA,CAACL,UAAU;MAAC2D,OAAO,EAAC,IAAI;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,EAAC;IAExC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbrD,OAAA,CAACjB,QAAQ;MAAC0E,QAAQ,eAAEzD,OAAA,CAACJ,gBAAgB;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAAAJ,QAAA,eACvCjD,OAAA,CAACP,SAAS;QAACiE,QAAQ,EAAC,IAAI;QAAAT,QAAA,EACrB3B,SAAS,gBACRtB,OAAA,CAACF,MAAM,CAAC6D,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YACVC,QAAQ,EAAE,GAAG;YACbC,IAAI,EAAE;UACR,CAAE;UAAAjB,QAAA,eAEFjD,OAAA,CAACH,GAAG;YACF0D,EAAE,EAAE;cACFY,OAAO,EAAE,MAAM;cACfC,cAAc,EAAE,QAAQ;cACxBC,UAAU,EAAE,QAAQ;cACpBC,SAAS,EAAE,OAAO;cAClBC,aAAa,EAAE,QAAQ;cACvBC,GAAG,EAAE;YACP,CAAE;YAAAvB,QAAA,gBAEFjD,OAAA,CAACJ,gBAAgB;cAAC6E,IAAI,EAAE;YAAG;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9BrD,OAAA,CAACL,UAAU;cAAC2D,OAAO,EAAC,IAAI;cAACoB,KAAK,EAAC,eAAe;cAAAzB,QAAA,EAAC;YAE/C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,GACX3B,KAAK,CAACiD,MAAM,IAAI,CAAC,IAAInD,MAAM,CAACmD,MAAM,IAAI,CAAC,gBACzC3E,OAAA,CAACa,YAAY;UACX+D,KAAK,EAAC,6BAA6B;UACnCC,WAAW,EAAC,oIAAoI;UAChJC,GAAG,EAAC;QAAmD;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,gBAEFrD,OAAA,CAACN,IAAI;UAACqF,SAAS;UAACC,OAAO,EAAE,CAAE;UAAA/B,QAAA,gBACzBjD,OAAA,CAACN,IAAI;YAACuF,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAnC,QAAA,eAC9BjD,OAAA,CAACjB,QAAQ;cAAC0E,QAAQ,eAAEzD,OAAA,CAACJ,gBAAgB;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,eACvCjD,OAAA,CAACC,gBAAgB;gBACf2E,KAAK,EAAC,cAAc;gBACpBS,KAAK,EAAE7D,MAAM,CAAC8D,MAAM,CAClB,CAACD,KAAK,EAAEE,KAAK,KAAKF,KAAK,GAAGE,KAAK,CAACC,UAAU,EAC1C,CACF,CAAE;gBACFd,KAAK,EAAE,MAAO;gBACde,IAAI,EAAE;cAAyB;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAEPrD,OAAA,CAACN,IAAI;YAACuF,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAnC,QAAA,eAC9BjD,OAAA,CAACjB,QAAQ;cAAC0E,QAAQ,eAAEzD,OAAA,CAACJ,gBAAgB;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,eACvCjD,OAAA,CAACC,gBAAgB;gBACf2E,KAAK,EAAC,aAAa;gBACnBS,KAAK,EAAE3D,KAAK,CAAC4D,MAAM,CACjB,CAACD,KAAK,EAAEK,IAAI,KAAKL,KAAK,GAAGK,IAAI,CAACC,UAAU,EACxC,CACF,CAAE;gBACFjB,KAAK,EAAC,OAAO;gBACbe,IAAI,EAAE;cAAwB;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAePrD,OAAA,CAACN,IAAI;YAACuF,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAACQ,EAAE,EAAE,CAAE;YAAA3C,QAAA,eAC9BjD,OAAA,CAACjB,QAAQ;cAAC0E,QAAQ,eAAEzD,OAAA,CAACJ,gBAAgB;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,eACvCjD,OAAA,CAACI,kBAAkB;gBACjBwE,KAAK,EAAC,aAAa;gBACnBiB,SAAS,EAAC,6MAA6M;gBACvNC,SAAS,EAAExD,UAAU,CAACyD,GAAG,CAAEd,IAAI,KAAM;kBACnCe,KAAK,EAAEf,IAAI,CAACgB,IAAI;kBAChBC,KAAK,EAAEjB,IAAI,CAACkB;gBACd,CAAC,CAAC;cAAE;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAEPrD,OAAA,CAACN,IAAI;YAACuF,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAACQ,EAAE,EAAE,CAAE;YAAA3C,QAAA,eAC9BjD,OAAA,CAACjB,QAAQ;cAAC0E,QAAQ,eAAEzD,OAAA,CAACJ,gBAAgB;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,eACvCjD,OAAA,CAACO,gBAAgB;gBACfqE,KAAK,EAAC,WAAW;gBACjBiB,SAAS,EAAC,6CAA6C;gBACvDC,SAAS,EAAE9D,OAAO,CAAC+D,GAAG,CAAEd,IAAI,KAAM;kBAChCe,KAAK,EAAEf,IAAI,CAACmB,OAAO;kBACnBF,KAAK,EAAEjB,IAAI,CAACkB;gBACd,CAAC,CAAC,CAAE;gBACJE,WAAW,EAAE,CACXhF,KAAK,CAACiF,OAAO,CAACC,OAAO,CAACC,IAAI,EAC1BnF,KAAK,CAACiF,OAAO,CAACG,OAAO,CAACD,IAAI,EAC1BnF,KAAK,CAACiF,OAAO,CAACI,IAAI,CAACC,SAAS,EAC5BtF,KAAK,CAACiF,OAAO,CAACM,IAAI,CAACJ,IAAI,EACvBnF,KAAK,CAACiF,OAAO,CAACO,OAAO,CAACL,IAAI,EAC1BnF,KAAK,CAACiF,OAAO,CAAC1D,KAAK,CAAC4D,IAAI;cACxB;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAEPrD,OAAA,CAACN,IAAI;YAACuF,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAACQ,EAAE,EAAE,CAAE;YAAA3C,QAAA,eAC9BjD,OAAA,CAACjB,QAAQ;cAAC0E,QAAQ,eAAEzD,OAAA,CAACJ,gBAAgB;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,eACvCjD,OAAA,CAACI,kBAAkB;gBACjBwE,KAAK,EAAC,kBAAkB;gBACxBiB,SAAS,EAAC,mPAAmP;gBAC7PC,SAAS,EAAElE,KAAK,CAACmE,GAAG,CAAEd,IAAI,KAAM;kBAC9Be,KAAK,EAAEf,IAAI,CAACgB,IAAI;kBAChBC,KAAK,EAAEjB,IAAI,CAACkB;gBACd,CAAC,CAAC;cAAE;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAEPrD,OAAA,CAACN,IAAI;YAACuF,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAACQ,EAAE,EAAE,CAAE;YAAA3C,QAAA,eAC9BjD,OAAA,CAACjB,QAAQ;cAAC0E,QAAQ,eAAEzD,OAAA,CAACJ,gBAAgB;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,eACvCjD,OAAA,CAACO,gBAAgB;gBACfqE,KAAK,EAAC,SAAS;gBACfiB,SAAS,EAAC,yDAAyD;gBACnEC,SAAS,EAAE5D,MAAM,CAAC6D,GAAG,CAAEd,IAAI,KAAM;kBAC/Be,KAAK,EAAEf,IAAI,CAAC6B,MAAM;kBAClBZ,KAAK,EAAEjB,IAAI,CAACkB;gBACd,CAAC,CAAC,CAAE;gBACJE,WAAW,EAAE,CACXhF,KAAK,CAACiF,OAAO,CAAC1D,KAAK,CAAC4D,IAAI,EACxBnF,KAAK,CAACiF,OAAO,CAACK,SAAS,CAACH,IAAI;cAC5B;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA+EH;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEhB;AAACjC,EAAA,CAxSuBD,aAAa;EAAA,QACrBxC,QAAQ;AAAA;AAAAoI,IAAA,GADA5F,aAAa;AAAA,IAAAjB,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAA6F,IAAA;AAAAC,YAAA,CAAA9G,EAAA;AAAA8G,YAAA,CAAA7G,GAAA;AAAA6G,YAAA,CAAA3G,GAAA;AAAA2G,YAAA,CAAA1G,GAAA;AAAA0G,YAAA,CAAAxG,GAAA;AAAAwG,YAAA,CAAAvG,GAAA;AAAAuG,YAAA,CAAArG,GAAA;AAAAqG,YAAA,CAAApG,GAAA;AAAAoG,YAAA,CAAAlG,GAAA;AAAAkG,YAAA,CAAAjG,IAAA;AAAAiG,YAAA,CAAA/F,IAAA;AAAA+F,YAAA,CAAA9F,IAAA;AAAA8F,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}