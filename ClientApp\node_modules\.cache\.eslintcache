[{"C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\serviceWorker.js": "3", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\reportWebVitals.js": "4", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\routes.js": "5", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\Context\\BudgetContext.js": "6", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\Context\\ProfileContext.js": "7", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\Context\\SearchContext.js": "8", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\index.js": "9", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\scroll-to-top\\index.js": "10", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\chart\\index.js": "11", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\AuthenticationData.ts": "12", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\ProfileData.ts": "13", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\SearchData.ts": "14", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\SignUpPage.js": "15", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\VerifyMailPage.js": "16", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\Page404.js": "17", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\VerifiyingMailPage.js": "18", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\AccessDenied.js": "19", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\AnalyticsPage.js": "20", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\VerifyPasswordChangingPage.js": "21", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\SettingsPage.js": "22", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\ForgotPasswordEmail.js": "23", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\TrackPage.js": "24", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\BundlesPage.js": "25", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\SearchResults.js": "26", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\profileUser.js": "27", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\LoginPage.js": "28", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\Profile.js": "29", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\RatingPage.js": "30", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Coupons\\AppManageCoupons.js": "31", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\customShadows.js": "32", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\globalStyles.js": "33", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\typography.js": "34", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\palette.js": "35", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\shadows.js": "36", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\layouts\\dashboard\\index.js": "37", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\chart\\useChart.js": "38", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\scroll-to-top\\ScrollToTop.js": "39", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\chart\\styles.js": "40", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\Api.js": "41", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\AnalyticsData.ts": "42", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\LinkData.ts": "43", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\PurchasesData.ts": "44", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\Context\\config.js": "45", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\Logo.js": "46", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\SearchNotFound.js": "47", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\PageNotFoundIllustration.js": "48", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\SeverErrorIllustration.js": "49", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\overrides\\index.js": "50", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\Appearance.js": "51", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\hooks\\useResponsive.js": "52", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Coupons\\EmptyContent.js": "53", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\CouponsData.ts": "54", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\RatingDialog.js": "55", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppWebsiteVisits.js": "56", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppCurrentVisits.js": "57", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppConversionRates.js": "58", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppDepositsTab.js": "59", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppTasks.js": "60", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppPurchasesTab.js": "61", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppLinksByProfile.js": "62", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppWidgetSummary.js": "63", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\utils\\formatTime.js": "64", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\ProfileAbout.js": "65", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\InviterFriends.js": "66", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Coupons\\CheckoutReserved.js": "67", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Account\\AccountSettings.js": "68", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Rating\\BookingCustomerReviews.js": "69", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Rating\\RatingDetailsReviewTrack.js": "70", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Link\\PhoneLinkDialog.js": "71", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Rating\\VerticalLinearStepper.js": "72", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\auth\\signup\\PhotoSelector.js": "73", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Rating\\RatingDetailsReview.js": "74", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\auth\\signup\\index.js": "75", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\index.js": "76", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\layouts\\dashboard\\DashboardLayout.js": "77", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\utils\\cssStyles.js": "78", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\auth\\login\\index.js": "79", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\overrides\\Table.js": "80", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\overrides\\Button.js": "81", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\overrides\\Card.js": "82", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\overrides\\Input.js": "83", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\overrides\\Tooltip.js": "84", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\overrides\\Backdrop.js": "85", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\overrides\\Paper.js": "86", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\overrides\\Autocomplete.js": "87", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\overrides\\Typography.js": "88", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\auth\\signup\\SquarePhotoSelector.js": "89", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Rating\\ThankYouCard.js": "90", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\ContactData.ts": "91", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\utils\\formatNumber.js": "92", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\iconify\\index.js": "93", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Contact\\AddCvDialog.js": "94", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\auth\\signup\\SignUpForm.js": "95", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppNewsUpdate.js": "96", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\auth\\signup\\Register2.js": "97", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\auth\\signup\\RegisterConfirm.js": "98", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\auth\\signup\\Register1.js": "99", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Contact\\ContactSection.js": "100", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppTrafficBySite.js": "101", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppLinksByPublicProfile.js": "102", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppOrderTimeline.js": "103", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppCurrentSubject.js": "104", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppProfileCard.js": "105", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppBundleWidget.js": "106", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\auth\\signup\\Register3.js": "107", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\layouts\\dashboard\\header\\index.js": "108", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\layouts\\dashboard\\nav\\index.js": "109", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\auth\\login\\LoginForm.js": "110", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\iconify\\Iconify.js": "111", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\Scrollbar.js": "112", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\layouts\\dashboard\\header\\AccountPopover.js": "113", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\layouts\\dashboard\\header\\Searchbar.js": "114", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\layouts\\dashboard\\header\\PalastinePopover.js": "115", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\nav-section\\index.js": "116", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\nav-section\\NavSection.js": "117", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\nav-section\\styles.js": "118", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Link\\WhatsAppLinkDialog.js": "119", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Link\\EmailLinkDialog.js": "120"}, {"size": 673, "mtime": *************, "results": "121", "hashOfConfig": "122"}, {"size": 1362, "mtime": *************, "results": "123", "hashOfConfig": "122"}, {"size": 5175, "mtime": *************, "results": "124", "hashOfConfig": "122"}, {"size": 377, "mtime": 1736848440961, "results": "125", "hashOfConfig": "122"}, {"size": 6309, "mtime": 1753906182663, "results": "126", "hashOfConfig": "122"}, {"size": 679, "mtime": 1736848440933, "results": "127", "hashOfConfig": "122"}, {"size": 1970, "mtime": 1736848440934, "results": "128", "hashOfConfig": "122"}, {"size": 1377, "mtime": 1736848440934, "results": "129", "hashOfConfig": "122"}, {"size": 1214, "mtime": 1736848440981, "results": "130", "hashOfConfig": "122"}, {"size": 42, "mtime": 1736848440948, "results": "131", "hashOfConfig": "122"}, {"size": 248, "mtime": 1736848440942, "results": "132", "hashOfConfig": "122"}, {"size": 10624, "mtime": 1754072336073, "results": "133", "hashOfConfig": "122"}, {"size": 3061, "mtime": 1753466171115, "results": "134", "hashOfConfig": "122"}, {"size": 1830, "mtime": 1753906583525, "results": "135", "hashOfConfig": "122"}, {"size": 2223, "mtime": 1753466171125, "results": "136", "hashOfConfig": "122"}, {"size": 3730, "mtime": 1753818577025, "results": "137", "hashOfConfig": "122"}, {"size": 1830, "mtime": 1753825180292, "results": "138", "hashOfConfig": "122"}, {"size": 2857, "mtime": 1753466171126, "results": "139", "hashOfConfig": "122"}, {"size": 1690, "mtime": 1753466171118, "results": "140", "hashOfConfig": "122"}, {"size": 13730, "mtime": 1754074658767, "results": "141", "hashOfConfig": "122"}, {"size": 9093, "mtime": 1753524662778, "results": "142", "hashOfConfig": "122"}, {"size": 10250, "mtime": 1753466171124, "results": "143", "hashOfConfig": "122"}, {"size": 3750, "mtime": 1753466171121, "results": "144", "hashOfConfig": "122"}, {"size": 17865, "mtime": 1753899702064, "results": "145", "hashOfConfig": "122"}, {"size": 8231, "mtime": *************, "results": "146", "hashOfConfig": "122"}, {"size": 13109, "mtime": 1753906552838, "results": "147", "hashOfConfig": "122"}, {"size": 111086, "mtime": 1754072432115, "results": "148", "hashOfConfig": "122"}, {"size": 2197, "mtime": 1753466171121, "results": "149", "hashOfConfig": "122"}, {"size": 59428, "mtime": 1754074630857, "results": "150", "hashOfConfig": "122"}, {"size": 32368, "mtime": 1754072584512, "results": "151", "hashOfConfig": "122"}, {"size": 6740, "mtime": 1753823958535, "results": "152", "hashOfConfig": "122"}, {"size": 1261, "mtime": 1736848440981, "results": "153", "hashOfConfig": "122"}, {"size": 1306, "mtime": 1736848440981, "results": "154", "hashOfConfig": "122"}, {"size": 2435, "mtime": 1736848440985, "results": "155", "hashOfConfig": "122"}, {"size": 2855, "mtime": 1736848440985, "results": "156", "hashOfConfig": "122"}, {"size": 3000, "mtime": 1736848440985, "results": "157", "hashOfConfig": "122"}, {"size": 46, "mtime": 1736848440953, "results": "158", "hashOfConfig": "122"}, {"size": 4415, "mtime": 1736848440943, "results": "159", "hashOfConfig": "122"}, {"size": 335, "mtime": 1736848440946, "results": "160", "hashOfConfig": "122"}, {"size": 2070, "mtime": 1736848440942, "results": "161", "hashOfConfig": "122"}, {"size": 2461, "mtime": 1736848440932, "results": "162", "hashOfConfig": "122"}, {"size": 9924, "mtime": 1754074611974, "results": "163", "hashOfConfig": "122"}, {"size": 7438, "mtime": 1754072397275, "results": "164", "hashOfConfig": "122"}, {"size": 2182, "mtime": 1736848440936, "results": "165", "hashOfConfig": "122"}, {"size": 390, "mtime": 1752421022024, "results": "166", "hashOfConfig": "122"}, {"size": 655, "mtime": 1753466171115, "results": "167", "hashOfConfig": "122"}, {"size": 778, "mtime": 1736848440959, "results": "168", "hashOfConfig": "122"}, {"size": 161314, "mtime": 1742047069710, "results": "169", "hashOfConfig": "122"}, {"size": 184262, "mtime": 1736848440942, "results": "170", "hashOfConfig": "122"}, {"size": 662, "mtime": 1736848440984, "results": "171", "hashOfConfig": "122"}, {"size": 45657, "mtime": 1754072037526, "results": "172", "hashOfConfig": "122"}, {"size": 1221, "mtime": *************, "results": "173", "hashOfConfig": "122"}, {"size": 1438, "mtime": 1736848440965, "results": "174", "hashOfConfig": "122"}, {"size": 8336, "mtime": 1754072560581, "results": "175", "hashOfConfig": "122"}, {"size": 17088, "mtime": 1736848440958, "results": "176", "hashOfConfig": "122"}, {"size": 1335, "mtime": 1736848440971, "results": "177", "hashOfConfig": "122"}, {"size": 2366, "mtime": 1736848440968, "results": "178", "hashOfConfig": "122"}, {"size": 1377, "mtime": 1736848440968, "results": "179", "hashOfConfig": "122"}, {"size": 1620, "mtime": 1736848440969, "results": "180", "hashOfConfig": "122"}, {"size": 5347, "mtime": 1736848440971, "results": "181", "hashOfConfig": "122"}, {"size": 1689, "mtime": 1736848440970, "results": "182", "hashOfConfig": "122"}, {"size": 8611, "mtime": 1753902826582, "results": "183", "hashOfConfig": "122"}, {"size": 2147, "mtime": 1736848440971, "results": "184", "hashOfConfig": "122"}, {"size": 663, "mtime": 1736848440986, "results": "185", "hashOfConfig": "122"}, {"size": 416, "mtime": 1736848440958, "results": "186", "hashOfConfig": "122"}, {"size": 2672, "mtime": 1736848440972, "results": "187", "hashOfConfig": "122"}, {"size": 4729, "mtime": 1753819212829, "results": "188", "hashOfConfig": "122"}, {"size": 5072, "mtime": 1736848440963, "results": "189", "hashOfConfig": "122"}, {"size": 12296, "mtime": 1736848440966, "results": "190", "hashOfConfig": "122"}, {"size": 4365, "mtime": 1736848440966, "results": "191", "hashOfConfig": "122"}, {"size": 23609, "mtime": 1753902610178, "results": "192", "hashOfConfig": "122"}, {"size": 2943, "mtime": 1736848440966, "results": "193", "hashOfConfig": "122"}, {"size": 3792, "mtime": 1753903101013, "results": "194", "hashOfConfig": "122"}, {"size": 5053, "mtime": 1736848440966, "results": "195", "hashOfConfig": "122"}, {"size": 340, "mtime": *************, "results": "196", "hashOfConfig": "122"}, {"size": 865, "mtime": 1736848440972, "results": "197", "hashOfConfig": "122"}, {"size": 1780, "mtime": 1753466171117, "results": "198", "hashOfConfig": "122"}, {"size": 2593, "mtime": 1736848440986, "results": "199", "hashOfConfig": "122"}, {"size": 53, "mtime": 1736848440976, "results": "200", "hashOfConfig": "122"}, {"size": 341, "mtime": 1736848440984, "results": "201", "hashOfConfig": "122"}, {"size": 1156, "mtime": 1736848440982, "results": "202", "hashOfConfig": "122"}, {"size": 852, "mtime": 1736848440982, "results": "203", "hashOfConfig": "122"}, {"size": 1776, "mtime": 1736848440983, "results": "204", "hashOfConfig": "122"}, {"size": 360, "mtime": 1736848440984, "results": "205", "hashOfConfig": "122"}, {"size": 419, "mtime": 1736848440982, "results": "206", "hashOfConfig": "122"}, {"size": 314, "mtime": 1736848440983, "results": "207", "hashOfConfig": "122"}, {"size": 289, "mtime": 1736848440982, "results": "208", "hashOfConfig": "122"}, {"size": 365, "mtime": 1736848440984, "results": "209", "hashOfConfig": "122"}, {"size": 2454, "mtime": 1753466171133, "results": "210", "hashOfConfig": "122"}, {"size": 2005, "mtime": 1736848440966, "results": "211", "hashOfConfig": "122"}, {"size": 2826, "mtime": 1754071983674, "results": "212", "hashOfConfig": "122"}, {"size": 1382, "mtime": 1736848440986, "results": "213", "hashOfConfig": "122"}, {"size": 38, "mtime": 1736848440944, "results": "214", "hashOfConfig": "122"}, {"size": 7855, "mtime": 1754071971428, "results": "215", "hashOfConfig": "122"}, {"size": 3491, "mtime": 1753818003641, "results": "216", "hashOfConfig": "122"}, {"size": 2897, "mtime": 1736848440969, "results": "217", "hashOfConfig": "122"}, {"size": 2946, "mtime": 1736848440978, "results": "218", "hashOfConfig": "122"}, {"size": 205, "mtime": 1736848440979, "results": "219", "hashOfConfig": "122"}, {"size": 8106, "mtime": 1736848440978, "results": "220", "hashOfConfig": "122"}, {"size": 7678, "mtime": 1736848440964, "results": "221", "hashOfConfig": "122"}, {"size": 1815, "mtime": 1736848440971, "results": "222", "hashOfConfig": "122"}, {"size": 5161, "mtime": 1753466171132, "results": "223", "hashOfConfig": "122"}, {"size": 2128, "mtime": 1736848440970, "results": "224", "hashOfConfig": "122"}, {"size": 2301, "mtime": 1736848440968, "results": "225", "hashOfConfig": "122"}, {"size": 3382, "mtime": 1736848440970, "results": "226", "hashOfConfig": "122"}, {"size": 8779, "mtime": 1753899702067, "results": "227", "hashOfConfig": "122"}, {"size": 8389, "mtime": 1736848440979, "results": "228", "hashOfConfig": "122"}, {"size": 2828, "mtime": 1736848440952, "results": "229", "hashOfConfig": "122"}, {"size": 5620, "mtime": 1736848440953, "results": "230", "hashOfConfig": "122"}, {"size": 8026, "mtime": 1753906257081, "results": "231", "hashOfConfig": "122"}, {"size": 647, "mtime": 1736848440944, "results": "232", "hashOfConfig": "122"}, {"size": 1541, "mtime": 1736848440941, "results": "233", "hashOfConfig": "122"}, {"size": 3476, "mtime": 1753466171118, "results": "234", "hashOfConfig": "122"}, {"size": 4045, "mtime": 1736848440952, "results": "235", "hashOfConfig": "122"}, {"size": 4606, "mtime": 1736848440952, "results": "236", "hashOfConfig": "122"}, {"size": 41, "mtime": 1736848440946, "results": "237", "hashOfConfig": "122"}, {"size": 2696, "mtime": 1753466171116, "results": "238", "hashOfConfig": "122"}, {"size": 690, "mtime": 1736848440946, "results": "239", "hashOfConfig": "122"}, {"size": 24154, "mtime": 1753902770541, "results": "240", "hashOfConfig": "122"}, {"size": 6378, "mtime": 1753902704716, "results": "241", "hashOfConfig": "122"}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, "hsp3k7", {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "249"}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "262", "usedDeprecatedRules": "245"}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "269", "usedDeprecatedRules": "245"}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "288"}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "301"}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "311"}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "315"}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "249"}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "325", "usedDeprecatedRules": "249"}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "329"}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "345"}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "385"}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "419", "usedDeprecatedRules": "245"}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "441"}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "445", "usedDeprecatedRules": "245"}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "464", "usedDeprecatedRules": "245"}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "468", "usedDeprecatedRules": "245"}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "472"}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "479"}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "546"}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "553", "usedDeprecatedRules": "245"}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "557", "usedDeprecatedRules": "245"}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "561", "usedDeprecatedRules": "245"}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "565"}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "572"}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "582", "usedDeprecatedRules": "245"}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "249"}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "589", "usedDeprecatedRules": "245"}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "608"}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "612", "usedDeprecatedRules": "245"}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "616", "usedDeprecatedRules": "245"}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "245"}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "629"}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\index.js", [], [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\App.js", [], [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\serviceWorker.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\routes.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\Context\\BudgetContext.js", ["633"], [], "import React, { createContext, useContext, useState, useEffect } from \"react\";\r\nimport { useProfile } from \"./ProfileContext\";\r\n\r\nconst BudgetContext = createContext();\r\n\r\nexport const useBudget = () => useContext(BudgetContext);\r\n\r\nexport const BudgetProvider = ({ children }) => {\r\n    const { profile } = useProfile();\r\n    const [budget, setBudget] = useState(0.0);\r\n\r\n    useEffect(() => {\r\n        fetchProfile();\r\n    }, [profile]);\r\n\r\n    const fetchProfile = async () => {\r\n        setBudget(profile?.budget);\r\n    };\r\n\r\n    return (\r\n        <BudgetContext.Provider value={{ budget, setBudget }}>\r\n            {children}\r\n        </BudgetContext.Provider>\r\n    );\r\n};\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\Context\\ProfileContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\Context\\SearchContext.js", ["634"], [], "import React, { createContext, useContext, useState, useEffect } from \"react\";\r\nimport { PostSearch, GetProfileFromSearch } from \"../SearchData.ts\";\r\nimport { useProfile } from \"./ProfileContext\";\r\n\r\nconst SearchContext = createContext();\r\n\r\nexport const useSearch = () => useContext(SearchContext);\r\n\r\nexport const SearchProvider = ({ children }) => {\r\n    const { profile } = useProfile();\r\n    const [searchResults, setSearchResults] = useState([]);\r\n    const [loading, setLoading] = useState(false);\r\n    const [error, setError] = useState(null);\r\n\r\n    const fetchSearchResults = async (query) => {\r\n        setLoading(true);\r\n        setError(null);\r\n        try {\r\n            const response = await GetProfileFromSearch(query);\r\n            setSearchResults(response.data);\r\n            await PostSearch({\r\n                UserId: profile.id,\r\n                Query: query,\r\n                Date: new Date(),\r\n            });\r\n        } catch (err) {\r\n            setError(\"Error fetching search results\");\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <SearchContext.Provider\r\n            value={{\r\n                searchResults,\r\n                loading,\r\n                error,\r\n                fetchSearchResults,\r\n            }}\r\n        >\r\n            {children}\r\n        </SearchContext.Provider>\r\n    );\r\n};\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\scroll-to-top\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\chart\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\AuthenticationData.ts", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\ProfileData.ts", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\SearchData.ts", ["635"], [], "import api from \"./Api\";\r\nimport { BASE_URL } from \"./Context/config\";\r\n\r\nexport interface PostSearch {\r\n  UserId: number;\r\n  Query: string;\r\n  Date: string;\r\n}\r\n\r\nexport const GetProfileFromSearch = async (search: string) => {\r\n  console.log(search);\r\n  try {\r\n    const response = await api.get(`${BASE_URL}/Search/Search`, {\r\n      params: {\r\n        search: search,\r\n      },\r\n      headers: {\r\n        \"Cache-Control\": \"no-cache\",\r\n        Pragma: \"no-cache\",\r\n        withCredentials: true,\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.log(\"Error:\", error.message);\r\n  }\r\n};\r\n\r\nexport const PostSearch = async (search: PostSearch) => {\r\n  const data = {\r\n    UserId: search.UserId,\r\n    Query: search.Query,\r\n    Date: search.Date,\r\n  };\r\n\r\n  try {\r\n    var authToken = getAuthToken();\r\n    const response = await api.post(`${BASE_URL}/Search/PostSearch`, data, {\r\n      headers: {\r\n        Authorization: `Bearer ${authToken}`,\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n    return response;\r\n  } catch (error) {\r\n    return { error: error.message };\r\n  }\r\n};\r\n\r\nexport const GetSearchQueries = async () => {\r\n  try {\r\n    var authToken = getAuthToken();\r\n    const response = await api.get(`${BASE_URL}/Search/GetSearchQueries`, {\r\n      headers: {\r\n        Authorization: `Bearer ${authToken}`,\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    return { error: error.message };\r\n  }\r\n};\r\n\r\nexport function getAuthToken() {\r\n  const cookies = document.cookie.split(\";\");\r\n\r\n  for (let i = 0; i < cookies.length; i++) {\r\n    const cookie = cookies[i].trim();\r\n    if (cookie.startsWith(\"authToken=\")) {\r\n      return cookie.substring(\"authToken=\".length, cookie.length);\r\n    }\r\n  }\r\n\r\n  return null;\r\n}\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\SignUpPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\VerifyMailPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\Page404.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\VerifiyingMailPage.js", ["636"], [], "import { Helmet } from \"react-helmet-async\";\r\nimport { styled } from \"@mui/material/styles\";\r\nimport {\r\n  Typography,\r\n  Container,\r\n  CircularProgress,\r\n  Button,\r\n  Box,\r\n} from \"@mui/material\";\r\nimport SeverErrorIllustration from \"../components/SeverErrorIllustration\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { useNavigate, useParams } from \"react-router-dom\";\r\nimport { verifyEmail } from \"../AuthenticationData.ts\";\r\nimport Logo from \"../components/Logo\";\r\n\r\nconst StyledContent = styled(\"div\")(({ theme }) => ({\r\n  maxWidth: 480,\r\n  margin: \"auto\",\r\n  minHeight: \"100vh\",\r\n  display: \"flex\",\r\n  justifyContent: \"center\",\r\n  flexDirection: \"column\",\r\n  padding: theme.spacing(12, 0),\r\n}));\r\n\r\nconst VerifyingMail = () => {\r\n  const navigate = useNavigate();\r\n  const { token } = useParams();\r\n\r\n  const [verificationStatus, setVerificationStatus] = useState(\"pending\");\r\n\r\n  useEffect(() => {\r\n    verify();\r\n  }, []);\r\n\r\n  const verify = async () => {\r\n    try {\r\n      const response = await verifyEmail(token);\r\n      if (response != null) {\r\n        setVerificationStatus(\"success\");\r\n      }\r\n      if (response.error) {\r\n        setVerificationStatus(\"error\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error verifying email:\", error);\r\n      setVerificationStatus(\"error\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Helmet>\r\n        <title>IDigics | Verifying Email</title>\r\n      </Helmet>\r\n\r\n      <Box sx={{ px: 3, py: 2, display: \"inline-flex\" }}>\r\n        <Logo />\r\n      </Box>\r\n\r\n      <Container sx={{ marginTop: \"-100px\" }}>\r\n        <StyledContent sx={{ textAlign: \"center\", alignItems: \"center\" }}>\r\n          <Typography variant=\"h3\" paragraph color=\"primary\">\r\n            Verifying Your Email\r\n          </Typography>\r\n\r\n          <SeverErrorIllustration sx={{ height: 260, my: { xs: 5, sm: 10 } }} />\r\n\r\n          {verificationStatus === \"success\" && (\r\n            <>\r\n              <Typography sx={{ color: \"text.secondary\" }}>\r\n                Thank you for verifiying your email\r\n              </Typography>\r\n              <Typography sx={{ color: \"text.secondary\" }}>\r\n                Please enjoy using IDigics\r\n              </Typography>\r\n\r\n              <Button\r\n                size=\"large\"\r\n                variant=\"contained\"\r\n                onClick={() => {\r\n                  navigate(\"/Login\");\r\n                }}\r\n              >\r\n                Home\r\n              </Button>\r\n            </>\r\n          )}\r\n          {verificationStatus === \"pending\" && <CircularProgress />}\r\n          {verificationStatus === \"error\" && (\r\n            <Typography variant=\"body1\" color=\"error\">\r\n              Error verifying email. Please try again later.\r\n            </Typography>\r\n          )}\r\n        </StyledContent>\r\n      </Container>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default VerifyingMail;\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\AccessDenied.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\AnalyticsPage.js", ["637", "638", "639", "640", "641", "642"], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\VerifyPasswordChangingPage.js", ["643", "644"], [], "import { Helmet } from \"react-helmet-async\";\r\nimport {\r\n  <PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON>,\r\n  <PERSON>,\r\n  <PERSON>Header,\r\n  CardContent,\r\n  Box,\r\n  Typography,\r\n  InputAdornment,\r\n  IconButton,\r\n  TextField,\r\n} from \"@mui/material\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { useNavigate, useParams } from \"react-router-dom\";\r\nimport { VerifyPasswordChanging } from \"../AuthenticationData.ts\";\r\nimport { ToastContainer, toast } from \"react-toastify\";\r\n\r\nconst VerifyPasswordChangingPage = () => {\r\n  const navigate = useNavigate();\r\n  const { token } = useParams();\r\n  const [password, setPassword] = useState(\"\");\r\n  const [confirmPassword, setConfirmPassword] = useState(\"\");\r\n  const [passwordChanged, setPasswordChanged] = useState(false);\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\r\n\r\n  const [passwordError, setPasswordError] = useState(\"\");\r\n  const [strength, setStrength] = useState(\"\");\r\n\r\n  useEffect(() => {\r\n    validatePassword(password);\r\n  }, [password, confirmPassword]);\r\n\r\n  const validatePassword = (password) => {\r\n    let strengthIndicator = -1;\r\n    let upper = false,\r\n      lower = false,\r\n      numbers = false,\r\n      specialChars = false,\r\n      PasswordConfirmed = false;\r\n\r\n    if (/[A-Z]/.test(password)) upper = true;\r\n    if (/[a-z]/.test(password)) lower = true;\r\n    if (/\\d/.test(password)) numbers = true;\r\n    if (/[!@#$%^&*()_+\\-=\\]{};':\"\\\\|,.<>\\/?]/.test(password))\r\n      specialChars = true;\r\n\r\n    if (\r\n      password !== \"\" &&\r\n      confirmPassword !== \"\" &&\r\n      password === confirmPassword\r\n    ) {\r\n      PasswordConfirmed = true;\r\n    }\r\n\r\n    if (password.length >= 8) {\r\n      strengthIndicator++;\r\n      if (password.length > 5) strengthIndicator++;\r\n    }\r\n    if (upper && lower && numbers && specialChars) strengthIndicator++;\r\n\r\n    setStrength([\"weak\", \"medium\", \"strong\"][strengthIndicator] || \"\");\r\n\r\n    switch (true) {\r\n      case password.length < 8:\r\n        setPasswordError(\"Password must be at least 8 characters long.\");\r\n        break;\r\n      case !specialChars:\r\n        setPasswordError(\r\n          \"Password must contain at least one special character.\"\r\n        );\r\n        break;\r\n      case !upper:\r\n        setPasswordError(\r\n          \"Password must contain at least one upper case character.\"\r\n        );\r\n        break;\r\n      case !lower:\r\n        setPasswordError(\r\n          \"Password must contain at least one lower case character.\"\r\n        );\r\n        break;\r\n      case !numbers:\r\n        setPasswordError(\"Password must contain numbers.\");\r\n        break;\r\n      case !PasswordConfirmed:\r\n        setPasswordError(\"Please Confirm your password.\");\r\n        break;\r\n      default:\r\n        setPasswordError(\"\");\r\n        break;\r\n    }\r\n  };\r\n\r\n  const handleConfirm = async () => {\r\n    // Validate passwords before sending\r\n    if (!password || !confirmPassword) {\r\n      toast.error(\"Please fill in both password fields\", {\r\n        position: \"top-center\",\r\n        autoClose: 3000,\r\n      });\r\n      return;\r\n    }\r\n\r\n    if (password !== confirmPassword) {\r\n      toast.error(\"Passwords do not match\", {\r\n        position: \"top-center\",\r\n        autoClose: 3000,\r\n      });\r\n      return;\r\n    }\r\n\r\n    if (passwordError) {\r\n      toast.error(passwordError, {\r\n        position: \"top-center\",\r\n        autoClose: 3000,\r\n      });\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await VerifyPasswordChanging(confirmPassword, token);\r\n      if (response.status === 200) {\r\n        toast.success(\"Password changed successfully\", {\r\n          position: \"top-center\",\r\n          autoClose: 2000,\r\n        });\r\n        setPassword(\"\");\r\n        setConfirmPassword(\"\");\r\n        setPasswordChanged(true);\r\n      } else {\r\n        // Handle error responses\r\n        let errorMessage = \"Error changing the password\";\r\n        try {\r\n          if (response.data && response.data.error) {\r\n            errorMessage = response.data.error;\r\n          } else if (response.data && response.data.message) {\r\n            errorMessage = response.data.message;\r\n          }\r\n        } catch (parseError) {\r\n          console.error(\"Error parsing response:\", parseError);\r\n        }\r\n\r\n        toast.error(errorMessage, {\r\n          position: \"top-center\",\r\n          autoClose: 4000,\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error changing password:\", error);\r\n      toast.error(\"An unexpected error occurred. Please try again later.\", {\r\n        position: \"top-center\",\r\n        autoClose: 3000,\r\n      });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Helmet>\r\n        <title>IDigics | Password Change</title>\r\n      </Helmet>\r\n\r\n      <Container\r\n        sx={{\r\n          display: \"flex\",\r\n          justifyContent: \"center\",\r\n          alignItems: \"center\",\r\n          height: \"70vh\",\r\n        }}\r\n      >\r\n        <Card sx={{ width: 600, borderRadius: 2, padding: \"40px\" }}>\r\n          <CardHeader\r\n            title=\"Password Change\"\r\n            subheader=\"Please remember your passwords next time :)\"\r\n          />\r\n          {passwordChanged ? (\r\n            <CardContent>\r\n              <Box mb={2}>\r\n                <Typography\r\n                  variant=\"overline\"\r\n                  sx={{\r\n                    mb: 3,\r\n                    display: \"block\",\r\n                    color: \"text.secondary\",\r\n                  }}\r\n                >\r\n                  Please check your email\r\n                </Typography>\r\n              </Box>\r\n              <Button\r\n                variant=\"contained\"\r\n                color=\"primary\"\r\n                onClick={() => {\r\n                  navigate(\"/Login\");\r\n                }}\r\n              >\r\n                Go to login\r\n              </Button>\r\n            </CardContent>\r\n          ) : (\r\n            <CardContent>\r\n              <Box mb={2}>\r\n                <TextField\r\n                  fullWidth\r\n                  name=\"password\"\r\n                  type={showPassword ? \"text\" : \"password\"}\r\n                  label=\"New Password\"\r\n                  value={password}\r\n                  onChange={(event) => {\r\n                    setPassword(event.target.value);\r\n                  }}\r\n                  error={!!passwordError}\r\n                  helperText={passwordError}\r\n                  InputProps={{\r\n                    endAdornment: (\r\n                      <InputAdornment position=\"end\">\r\n                        <IconButton\r\n                          onClick={() => setShowPassword(!showPassword)}\r\n                          edge=\"end\"\r\n                        >\r\n                          <i\r\n                            className={\r\n                              showPassword ? \"fas fa-eye-slash\" : \"fas fa-eye\"\r\n                            }\r\n                          />\r\n                        </IconButton>\r\n                      </InputAdornment>\r\n                    ),\r\n                  }}\r\n                />\r\n              </Box>\r\n              <div className={`bars ${strength}`}>\r\n                <div></div>\r\n              </div>\r\n              <Box mb={2}>\r\n                <TextField\r\n                  fullWidth\r\n                  name=\"confirmPassword\"\r\n                  type={showConfirmPassword ? \"text\" : \"password\"}\r\n                  label=\"Confirm Password\"\r\n                  value={confirmPassword}\r\n                  onChange={(event) => {\r\n                    setConfirmPassword(event.target.value);\r\n                  }}\r\n                  error={password !== confirmPassword && confirmPassword !== \"\"}\r\n                  helperText={\r\n                    password !== confirmPassword && confirmPassword !== \"\"\r\n                      ? \"Passwords do not match\"\r\n                      : \"\"\r\n                  }\r\n                  InputProps={{\r\n                    endAdornment: (\r\n                      <InputAdornment position=\"end\">\r\n                        <IconButton\r\n                          onClick={() =>\r\n                            setShowConfirmPassword(!showConfirmPassword)\r\n                          }\r\n                          edge=\"end\"\r\n                        >\r\n                          <i\r\n                            className={\r\n                              showConfirmPassword\r\n                                ? \"fas fa-eye-slash\"\r\n                                : \"fas fa-eye\"\r\n                            }\r\n                          />\r\n                        </IconButton>\r\n                      </InputAdornment>\r\n                    ),\r\n                  }}\r\n                />\r\n              </Box>\r\n              <Button\r\n                variant=\"contained\"\r\n                color=\"primary\"\r\n                onClick={handleConfirm}\r\n                disabled={!!passwordError || strength.trim() !== \"strong\"}\r\n              >\r\n                Confirm\r\n              </Button>\r\n            </CardContent>\r\n          )}\r\n        </Card>\r\n      </Container>\r\n      <ToastContainer />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default VerifyPasswordChangingPage;\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\SettingsPage.js", ["645", "646", "647", "648", "649", "650"], [], "import { Helmet } from \"react-helmet-async\";\r\nimport { useCallback, useState, lazy, startTransition, Suspense } from \"react\";\r\n\r\nimport { Grid, Typography, CircularProgress } from \"@mui/material\";\r\nimport { useNavigate } from \"react-router-dom\";\r\n\r\nimport {\r\n  Card,\r\n  But<PERSON>,\r\n  Box,\r\n  CardContent,\r\n  Container,\r\n  CardHeader,\r\n  Tab,\r\n  Tabs,\r\n} from \"@mui/material\";\r\n\r\nimport { GetDeposits, GetPurchases } from \"../PurchasesData.ts\";\r\nimport { useEffect } from \"react\";\r\n\r\nimport AppPurchasesTab from \"../sections/@dashboard/app/AppPurchasesTab\";\r\n// import ArrowForwardIcon from \"@mui/icons-material/ArrowForward\";\r\nimport { useProfile } from \"../Context/ProfileContext\";\r\nimport { fDateTime } from \"../utils/formatTime\";\r\n\r\nconst AppDepositsTab = lazy(() =>\r\n  import(\"../sections/@dashboard/app/AppDepositsTab\")\r\n);\r\n\r\nconst AccountSettings = lazy(() =>\r\n  import(\"../sections/@dashboard/Account/AccountSettings\")\r\n);\r\n\r\nconst reservdlist = [\r\n  {\r\n    id: 1,\r\n    name: \"<PERSON>\",\r\n    createdAt: \"2024-03-04\",\r\n    tokenId: \"abc123dfsfsdfdssfq\",\r\n  },\r\n  {\r\n    id: 2,\r\n    name: \"Jane Smith\",\r\n    createdAt: \"2024-03-03\",\r\n    tokenId: \"def456dfdsfsdfsdfdsf\",\r\n  },\r\n\r\n  // Add more user objects as needed\r\n];\r\n\r\n// ----------------------------------------------------------------------\r\n\r\nconst invoices = [\r\n  { id: 1, createdAt: \"2023-01-20\", price: 100 },\r\n  { id: 2, createdAt: \"2023-02-19\", price: 150 },\r\n  { id: 3, createdAt: \"2023-03-18\", price: 200 },\r\n  // Add more invoice objects as needed\r\n];\r\nconst fDate = (dateString) => {\r\n  const date = new Date(dateString);\r\n  const options = { day: \"2-digit\", month: \"long\", year: \"numeric\" };\r\n  return date.toLocaleDateString(\"en-US\", options);\r\n};\r\n\r\nconst fCurrency = (amount) => {\r\n  return `$${amount.toFixed(2)}`; // Format currency as needed\r\n};\r\n\r\nexport const SettingsPage = () => {\r\n  const { profile } = useProfile();\r\n  const navigate = useNavigate();\r\n  const [PurchasesPage, setPurchasesPage] = useState(0);\r\n  const [Deposits, setDeposits] = useState([]);\r\n  const [Purchases, setPurchases] = useState([]);\r\n  const [PurchasesRowsPerPage, setPurchasesRowsPerPage] = useState(5);\r\n\r\n  const [activeTab, setActiveTab] = useState(0);\r\n\r\n  const handleTabChange = (event, newValue) => {\r\n    setActiveTab(newValue);\r\n  };\r\n\r\n  const handlePurchasesPageChange = useCallback((event, value) => {\r\n    setPurchasesPage(value);\r\n  }, []);\r\n\r\n  const handlePurchasesRowsPerPageChange = useCallback((event) => {\r\n    setPurchasesRowsPerPage(event.target.value);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    startTransition(() => {\r\n      fetchDeposits();\r\n    });\r\n    startTransition(() => {\r\n      fetchPurchases();\r\n    });\r\n  }, []);\r\n\r\n  const fetchDeposits = async () => {\r\n    const response = await GetDeposits();\r\n    setDeposits(\r\n      response.data.map((deposit) => {\r\n        deposit.date = fDateTime(deposit.date);\r\n        return deposit;\r\n      })\r\n    );\r\n  };\r\n  const fetchPurchases = async () => {\r\n    const response = await GetPurchases();\r\n    setPurchases(\r\n      response.data.map((purchase) => {\r\n        purchase.date = fDateTime(purchase.date);\r\n        return purchase;\r\n      })\r\n    );\r\n  };\r\n\r\n  return (\r\n    <Container sx>\r\n      <Helmet>\r\n        <title> IDigics | Settings </title>\r\n      </Helmet>\r\n      <Typography variant=\"h5\" sx={{ mb: 4 }}>\r\n        Settings\r\n      </Typography>\r\n      <Box>\r\n        <Tabs\r\n          value={activeTab}\r\n          onChange={handleTabChange}\r\n          aria-label=\"Account tabs\"\r\n        >\r\n          <Tab label=\"Account\" />\r\n          <Tab label=\"Budget\" />\r\n        </Tabs>\r\n      </Box>\r\n\r\n      {activeTab === 0 && (\r\n        <Suspense callback={<CircularProgress />}>\r\n          <AccountSettings />\r\n        </Suspense>\r\n      )}\r\n\r\n      {activeTab === 1 && (\r\n        <Container sx={{ marginTop: \"25px\" }}>\r\n          <Grid container spacing={3}>\r\n            <Grid item xs={12} sm={12} md={8}>\r\n              {profile.category && (\r\n                <Card sx={{ p: 3, marginBottom: \"30px\" }}>\r\n                  <Typography\r\n                    variant=\"overline\"\r\n                    sx={{\r\n                      mb: 3,\r\n                      display: \"block\",\r\n                      color: \"text.secondary\",\r\n                    }}\r\n                  >\r\n                    Your Plan\r\n                  </Typography>\r\n                  <Typography variant=\"h4\">{profile.category}</Typography>\r\n                  {profile.category != \"Freelance\" &&\r\n                    profile.category != \"Enterprise\" && (\r\n                      <Box\r\n                        sx={{\r\n                          mt: { xs: 2, sm: 0 },\r\n                          position: {\r\n                            sm: \"absolute\",\r\n                          },\r\n                          top: { sm: 24 },\r\n                          right: { sm: 24 },\r\n                        }}\r\n                      >\r\n                        <Button\r\n                          size=\"small\"\r\n                          variant=\"outlined\"\r\n                          onClick={() => {\r\n                            navigate(\"/admin/bundles\");\r\n                          }}\r\n                        >\r\n                          Upgrade plan\r\n                        </Button>\r\n                      </Box>\r\n                    )}\r\n                </Card>\r\n              )}\r\n              <Card>\r\n                <CardHeader\r\n                  title=\"Deposits\"\r\n                  subheader=\"This table provides a comprehensive history of deposit transactions for tracking purposes.\"\r\n                />\r\n                <CardContent>\r\n                  <Grid item xs={12} md={12}>\r\n                    <Suspense callback={<CircularProgress />}>\r\n                      <AppDepositsTab deposits={Deposits} />\r\n                    </Suspense>\r\n                  </Grid>\r\n                </CardContent>\r\n              </Card>\r\n              <Card sx={{ marginTop: \"30px\" }}>\r\n                <CardHeader\r\n                  title=\"Purchases\"\r\n                  subheader=\"This table serves as a record of past purchases, allowing you to track your purchases history.\"\r\n                />\r\n                <CardContent>\r\n                  <Grid item xs={12} md={12}>\r\n                    <Suspense callback={<CircularProgress />}>\r\n                      <AppPurchasesTab\r\n                        purchases={Purchases}\r\n                        onPageChange={handlePurchasesPageChange}\r\n                        onRowsPerPageChange={handlePurchasesRowsPerPageChange}\r\n                        page={PurchasesPage}\r\n                        rowsPerPage={PurchasesRowsPerPage}\r\n                      />\r\n                    </Suspense>\r\n                  </Grid>\r\n                </CardContent>\r\n              </Card>\r\n            </Grid>\r\n\r\n            <Grid item xs={12} sm={12} md={4}>\r\n              {/* <Card>\r\n                                <CardContent>\r\n                                    <Stack spacing={3} alignItems=\"flex-end\">\r\n                                        <Typography\r\n                                            variant=\"subtitle1\"\r\n                                            sx={{ width: 1 }}\r\n                                        >\r\n                                            Invoice History\r\n                                        </Typography>\r\n\r\n                                        <Stack spacing={2} sx={{ width: 1 }}>\r\n                                            {invoices.map((invoice) => (\r\n                                                <Stack\r\n                                                    key={invoice.id}\r\n                                                    direction=\"row\"\r\n                                                    justifyContent=\"space-between\"\r\n                                                    sx={{ width: 1 }}\r\n                                                >\r\n                                                    <Typography\r\n                                                        variant=\"body2\"\r\n                                                        sx={{ minWidth: 160 }}\r\n                                                    >\r\n                                                        {fDate(\r\n                                                            invoice.createdAt\r\n                                                        )}\r\n                                                    </Typography>\r\n                                                    <Typography variant=\"body2\">\r\n                                                        {fCurrency(\r\n                                                            invoice.price\r\n                                                        )}\r\n                                                    </Typography>\r\n                                                    <RouterLink\r\n                                                        to=\"#\"\r\n                                                        style={{\r\n                                                            textDecoration:\r\n                                                                \"none\",\r\n                                                            color: \"#ff715b\",\r\n                                                        }}\r\n                                                    >\r\n                                                        PDF\r\n                                                    </RouterLink>\r\n                                                </Stack>\r\n                                            ))}\r\n                                        </Stack>\r\n\r\n                                        <Button\r\n                                            size=\"small\"\r\n                                            endIcon={<ArrowForwardIcon />}\r\n                                        >\r\n                                            All invoices\r\n                                        </Button>\r\n                                    </Stack>\r\n                                </CardContent>\r\n                            </Card> */}\r\n            </Grid>\r\n          </Grid>\r\n        </Container>\r\n      )}\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default SettingsPage;\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\ForgotPasswordEmail.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\TrackPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\BundlesPage.js", ["651"], [], "import { useEffect, useState, lazy, Suspense, startTransition } from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { Helmet } from \"react-helmet-async\";\r\nimport { PurchaseBundle } from \"../PurchasesData.ts\";\r\nimport { useProfile } from \"../Context/ProfileContext\";\r\nimport { useBudget } from \"../Context/BudgetContext\";\r\nimport {\r\n  Container,\r\n  Grid,\r\n  Typography,\r\n  CircularProgress,\r\n  Dialog,\r\n  DialogTitle,\r\n  DialogContent,\r\n  DialogActions,\r\n  Button,\r\n} from \"@mui/material\";\r\nimport { toast } from \"react-toastify\";\r\n\r\n// Lazy load toast container and toast\r\nconst ToastContainer = lazy(() =>\r\n  import(\"react-toastify\").then((module) => ({\r\n    default: module.ToastContainer,\r\n  }))\r\n);\r\n\r\n// Lazy load components that can be deferred\r\nconst EmojiPeopleIcon = lazy(() => import(\"@mui/icons-material/EmojiPeople\"));\r\nconst ApartmentIcon = lazy(() => import(\"@mui/icons-material/Apartment\"));\r\nconst BusinessCenterIcon = lazy(() =>\r\n  import(\"@mui/icons-material/BusinessCenter\")\r\n);\r\nconst EngineeringIcon = lazy(() => import(\"@mui/icons-material/Engineering\"));\r\nconst AppBundleWidget = lazy(() =>\r\n  import(\"../sections/@dashboard/app\").then((module) => ({\r\n    default: module.AppBundleWidget,\r\n  }))\r\n);\r\n\r\nexport const BundlesPage = () => {\r\n  const { profile, fetchProfile } = useProfile();\r\n  const { budget, setBudget } = useBudget();\r\n  const navigate = useNavigate();\r\n  const [openDialog, setOpenDialog] = useState(false);\r\n\r\n  const [reference, setReference] = useState(\"\");\r\n  const [amount, setAmount] = useState(\"\");\r\n\r\n  useEffect(() => {\r\n    localStorage.setItem(\"isCardVisible\", \"true\");\r\n  }, []);\r\n\r\n  const handlePurchase = async (ref, amount) => {\r\n    try {\r\n      await startTransition(async () => {\r\n        if (budget < amount) {\r\n          toast.error(\"Your budget is not enough to make this purchase!\", {\r\n            position: \"top-center\",\r\n            autoClose: 1000,\r\n          });\r\n          return;\r\n        }\r\n        if (profile.category === ref) {\r\n          toast.error(\"You already have this bundle!\", {\r\n            position: \"top-center\",\r\n            autoClose: 1000,\r\n          });\r\n          return;\r\n        }\r\n\r\n        const response = await PurchaseBundle({\r\n          UserId: profile.id,\r\n          Reference: ref,\r\n          Date: new Date(),\r\n          Amount: amount,\r\n          Country: profile.profile.country,\r\n        });\r\n\r\n        if (response) {\r\n          setBudget(response.data.newAmount);\r\n          toast.success(\"Congratulations!\", {\r\n            position: \"top-center\",\r\n            autoClose: 1000,\r\n          });\r\n        }\r\n\r\n        setOpenDialog(false);\r\n        fetchProfile();\r\n      });\r\n    } catch (error) {\r\n      if (error.redirectToLogin) {\r\n        navigate(\"/Login\");\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Container>\r\n        <Helmet>\r\n          <title> IDigics | Bundles </title>\r\n        </Helmet>\r\n        <Typography variant=\"h5\" sx={{ mb: 4 }}>\r\n          Bundles\r\n        </Typography>\r\n        <Grid container spacing={4}>\r\n          {/* <Grid item xs={12} sm={6} md={4}>\r\n                        <Suspense fallback={<CircularProgress />}>\r\n                            <AppBundleWidget\r\n                                title=\"Free\"\r\n                                amount={0}\r\n                                reference={\"Free\"}\r\n                                icon={<EmojiPeopleIcon fontSize=\"large\" />}\r\n                                data={[\r\n                                    \"include Social Links (Up to 5)\",\r\n                                    \"include Custom Links (1)\",\r\n                                    \"Media Pictures (Up to 5)\",\r\n                                    \"YouTube Integration (Links to videos)\",\r\n                                    \"Enhanced Biography (About)\",\r\n                                    \"Analytic Tracking (View performance)\",\r\n                                    \"Rating System (coupons)\",\r\n                                ]}\r\n                                isFreeBundle={true}\r\n                            />\r\n                        </Suspense>\r\n                    </Grid> */}\r\n\r\n          <Grid item xs={12} sm={6} md={4}>\r\n            <Suspense fallback={<CircularProgress />}>\r\n              <AppBundleWidget\r\n                title=\"Student\"\r\n                amount={25}\r\n                reference={\"Student\"}\r\n                icon={<BusinessCenterIcon fontSize=\"large\" />}\r\n                data={[\r\n                  \"include Social Links (Unlimited)\",\r\n                  \"include Custom Links (Up to 1)\",\r\n                  \"include Media Pictures (Up to 5)\",\r\n                  \"include YouTube Integration (Links to videos)\",\r\n                  \"include Enhanced Biography (About)\",\r\n                  \"Analytic Tracking (View performance)\",\r\n                  \"Rating Rating System (coupons)\",\r\n                ]}\r\n                setOpenDialog={setOpenDialog}\r\n                setReference={setReference}\r\n                setAmount={setAmount}\r\n                currentUserCategory={profile?.category}\r\n              />\r\n            </Suspense>\r\n          </Grid>\r\n\r\n          <Grid item xs={12} sm={6} md={4}>\r\n            <Suspense fallback={<CircularProgress />}>\r\n              <AppBundleWidget\r\n                title=\"Freelance\"\r\n                amount={35}\r\n                reference={\"Freelance\"}\r\n                icon={<EngineeringIcon fontSize=\"large\" />}\r\n                data={[\r\n                  \"include Social Links (Unlimited)\",\r\n                  \"include Custom Links (Up to 1)\",\r\n                  \"include Media Pictures (Up to 5)\",\r\n                  \"include YouTube Integration (Links to videos)\",\r\n                  \"include Enhanced Biography (About)\",\r\n                  \"include Rating System (coupons)\",\r\n                  \"Analytic Tracking (View performance)\",\r\n                ]}\r\n                setOpenDialog={setOpenDialog}\r\n                setReference={setReference}\r\n                setAmount={setAmount}\r\n                currentUserCategory={profile?.category}\r\n              />\r\n            </Suspense>\r\n          </Grid>\r\n\r\n          <Grid item xs={12} sm={6} md={4}>\r\n            <Suspense fallback={<CircularProgress />}>\r\n              <AppBundleWidget\r\n                title=\"Enterprise\"\r\n                amount={50}\r\n                reference={\"Enterprise\"}\r\n                article={\"Recommended\"}\r\n                icon={<ApartmentIcon fontSize=\"large\" />}\r\n                data={[\r\n                  \"include Social Links (Unlimited)\",\r\n                  \"include Custom Links (Up to 1)\",\r\n                  \"include Media Pictures (Up to 5)\",\r\n                  \"include YouTube Integration (Links to videos)\",\r\n                  \"include Enhanced Biography (About)\",\r\n                  \"include Analytic Tracking (View performance)\",\r\n                  \"include Rating System (coupons)\",\r\n                ]}\r\n                setOpenDialog={setOpenDialog}\r\n                setReference={setReference}\r\n                setAmount={setAmount}\r\n                currentUserCategory={profile?.category}\r\n              />\r\n            </Suspense>\r\n          </Grid>\r\n        </Grid>\r\n        <Suspense fallback={<CircularProgress />}>\r\n          <ToastContainer />\r\n        </Suspense>\r\n      </Container>\r\n      <Dialog\r\n        open={openDialog}\r\n        onClose={() => {\r\n          setOpenDialog(false);\r\n        }}\r\n      >\r\n        <DialogTitle color=\"primary\">Purchase</DialogTitle>\r\n\r\n        <DialogContent>\r\n          <Typography\r\n            sx={{\r\n              padding: \"20px\",\r\n              color: \"text.secondary\",\r\n            }}\r\n          >\r\n            Are you sure you want to purchase this bundle ? {reference}\r\n          </Typography>\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button\r\n            onClick={() => {\r\n              setOpenDialog(false);\r\n            }}\r\n          >\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            onClick={() => {\r\n              handlePurchase(reference, amount);\r\n            }}\r\n          >\r\n            Confirm\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n    </>\r\n  );\r\n};\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\SearchResults.js", ["652", "653"], [], "import { useEffect, useState } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON>,\r\n  <PERSON>,\r\n  Box,\r\n  Typography,\r\n  Tabs,\r\n  Tab,\r\n  Container,\r\n  Rating,\r\n  Stack,\r\n} from \"@mui/material\";\r\nimport LocationOnIcon from \"@mui/icons-material/LocationOn\";\r\nimport WorkOutlineIcon from \"@mui/icons-material/WorkOutline\";\r\nimport StarBorderIcon from \"@mui/icons-material/StarBorder\";\r\nimport Avatar from \"@mui/material/Avatar\";\r\nimport { Link } from \"react-router-dom\";\r\nimport { useSearch } from \"../Context/SearchContext\";\r\nimport SearchNotFound from \"./SearchNotFound\";\r\nimport { motion } from \"framer-motion\";\r\nimport EmojiPeopleIcon from \"@mui/icons-material/EmojiPeople\";\r\nimport EngineeringIcon from \"@mui/icons-material/Engineering\";\r\nimport ApartmentIcon from \"@mui/icons-material/Apartment\";\r\nimport BusinessCenterIcon from \"@mui/icons-material/BusinessCenter\";\r\nimport { useSearchParams } from \"react-router-dom\";\r\nimport { CircularProgress } from \"@mui/material\";\r\n\r\n// Helper component for result card\r\nconst ResultCard = ({ result, GetCategoryIcon }) => (\r\n  <Grid item xs={12}>\r\n    <motion.div\r\n      className=\"animate-on-scroll\"\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{\r\n        duration: 0.5,\r\n        ease: \"easeOut\",\r\n      }}\r\n    >\r\n      <Link\r\n        to={`/Profile/${result.userName}`}\r\n        target=\"_blank\"\r\n        rel=\"noreferrer\"\r\n        style={{\r\n          textDecoration: \"none\",\r\n          color: \"inherit\",\r\n        }}\r\n      >\r\n        <Card\r\n          sx={{\r\n            display: \"flex\",\r\n            flexDirection: \"row\",\r\n            alignItems: \"center\",\r\n            p: 3,\r\n            gap: \"10px\",\r\n            boxShadow: \"0px 4px 10px rgba(0, 0, 0, 0.1)\",\r\n            transition: \"box-shadow 0.3s ease\",\r\n            \"&:hover\": {\r\n              boxShadow: \"0px 8px 20px rgba(0, 0, 0, 0.2)\",\r\n            },\r\n          }}\r\n        >\r\n          <Stack\r\n            direction=\"row\"\r\n            alignItems=\"center\"\r\n            spacing={2}\r\n            sx={{ flex: 1 }}\r\n          >\r\n            <Avatar\r\n              alt={result.user.firstName + \" \" + result.user.lastName}\r\n              src={result.profilePicture}\r\n              sx={{\r\n                width: 80,\r\n                height: 80,\r\n              }}\r\n            />\r\n            <Stack direction=\"column\" spacing={1}>\r\n              <Typography variant=\"h6\">\r\n                {result.user.firstName} {result.user.lastName}{\" \"}\r\n                <Stack\r\n                  direction=\"row\"\r\n                  alignItems=\"baseline\"\r\n                  spacing={1}\r\n                  sx={{\r\n                    display: \"inline-flex\",\r\n                    marginLeft: 1,\r\n                  }}\r\n                >\r\n                  <Typography\r\n                    variant=\"caption\"\r\n                    sx={{\r\n                      color: \"rgba(20, 43, 58, 0.5)\",\r\n                    }}\r\n                  >\r\n                    @{result.userName}\r\n                  </Typography>\r\n                  <Typography\r\n                    variant=\"body2\"\r\n                    sx={{\r\n                      color: \"rgba(20, 43, 58, 0.5)\",\r\n                    }}\r\n                  >\r\n                    {GetCategoryIcon(result.user.category)}\r\n                  </Typography>\r\n                </Stack>\r\n              </Typography>\r\n              {result.user.rate > 0 && (\r\n                <Rating\r\n                  name=\"read-only\"\r\n                  value={result.user.rate}\r\n                  readOnly\r\n                  emptyIcon={<StarBorderIcon fontSize=\"inherit\" />}\r\n                  sx={{\r\n                    marginLeft: \"10px\",\r\n                    fontSize: \"13px\",\r\n                  }}\r\n                />\r\n              )}\r\n              <Box\r\n                sx={{\r\n                  display: \"flex\",\r\n                  alignItems: \"center\",\r\n                  gap: \"10px\",\r\n                }}\r\n              >\r\n                {result.country && (\r\n                  <>\r\n                    <LocationOnIcon\r\n                      sx={{\r\n                        fontSize: \"15px\",\r\n                      }}\r\n                    />\r\n                    <Typography\r\n                      variant=\"caption\"\r\n                      sx={{\r\n                        color: \"rgba(20, 43, 58, 0.5)\",\r\n                      }}\r\n                    >\r\n                      {result.country}\r\n                    </Typography>\r\n                  </>\r\n                )}\r\n              </Box>\r\n              <Box\r\n                sx={{\r\n                  display: \"flex\",\r\n                  alignItems: \"center\",\r\n                  gap: \"10px\",\r\n                }}\r\n              >\r\n                {result.occupation && (\r\n                  <>\r\n                    <WorkOutlineIcon\r\n                      sx={{\r\n                        fontSize: \"15px\",\r\n                      }}\r\n                    />\r\n                    <Typography\r\n                      variant=\"caption\"\r\n                      sx={{\r\n                        color: \"rgba(20, 43, 58, 0.5)\",\r\n                      }}\r\n                    >\r\n                      {result.occupation}\r\n                    </Typography>\r\n                  </>\r\n                )}\r\n              </Box>\r\n            </Stack>\r\n          </Stack>\r\n        </Card>\r\n      </Link>\r\n    </motion.div>\r\n  </Grid>\r\n);\r\n\r\nconst SearchResults = () => {\r\n  const { searchResults, fetchSearchResults, loading } = useSearch();\r\n  const [activeTab, setActiveTab] = useState(0); // Default to \"All\" tab\r\n  const [displayCount, setDisplayCount] = useState(5);\r\n  const [sortedResults, setSortedResults] = useState([]);\r\n  const [ratedResults, setRatedResults] = useState([]);\r\n  const [normalResults, setNormalResults] = useState([]);\r\n  const [searchParams] = useSearchParams();\r\n\r\n  // Fetch search results only when search parameters change\r\n  useEffect(() => {\r\n    const handleSearch = async () => {\r\n      if (searchParams && searchParams.toString() !== \"\") {\r\n        await fetchSearchResults(searchParams.get(\"q\"));\r\n      }\r\n    };\r\n    handleSearch(); // Call search function only when searchParams change\r\n  }, [searchParams]);\r\n\r\n  // Update sortedResults whenever searchResults or activeTab changes\r\n  useEffect(() => {\r\n    const { sorted, rated, normal } = sortResultsByTab(activeTab);\r\n    setSortedResults(sorted);\r\n    setRatedResults(rated);\r\n    setNormalResults(normal);\r\n  }, [searchResults, activeTab]);\r\n\r\n  const GetCategoryIcon = (category) => {\r\n    switch (category) {\r\n      case \"Free\":\r\n        return <EmojiPeopleIcon fontSize=\"Large\" />;\r\n      case \"Student\":\r\n        return <BusinessCenterIcon fontSize=\"Large\" />;\r\n      case \"Freelance\":\r\n        return <EngineeringIcon fontSize=\"Large\" />;\r\n      case \"Entrepreneur\":\r\n        return <ApartmentIcon fontSize=\"Large\" />;\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  const handleTabChange = (event, newValue) => {\r\n    setActiveTab(newValue);\r\n    setDisplayCount(5); // Reset display count when switching tabs\r\n  };\r\n\r\n  const sortResultsByTab = (tabValue) => {\r\n    const ratedAccounts = searchResults\r\n      .filter((result) => result.user.rate > 0)\r\n      .sort((a, b) => b.user.rate - a.user.rate);\r\n\r\n    const normalAccounts = searchResults\r\n      .filter((result) => !result.user.rate || result.user.rate === 0)\r\n      .sort((a, b) => a.userName.localeCompare(b.userName));\r\n\r\n    switch (tabValue) {\r\n      case 0: // \"All\" tab - show both sections\r\n        return {\r\n          sorted: [...ratedAccounts, ...normalAccounts],\r\n          rated: ratedAccounts,\r\n          normal: normalAccounts,\r\n        };\r\n      case 1: // \"Rated\" tab - show only rated accounts\r\n        return {\r\n          sorted: ratedAccounts,\r\n          rated: ratedAccounts,\r\n          normal: [],\r\n        };\r\n      default:\r\n        return {\r\n          sorted: searchResults,\r\n          rated: ratedAccounts,\r\n          normal: normalAccounts,\r\n        };\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Container>\r\n      <Typography variant=\"h5\" component=\"h1\" gutterBottom>\r\n        Search\r\n      </Typography>\r\n      <Typography component=\"h2\" color=\"textSecondary\">\r\n        Results for {searchParams.get(\"q\")}\r\n      </Typography>\r\n      {loading ? (\r\n        <Box\r\n          sx={{\r\n            display: \"flex\",\r\n            justifyContent: \"center\",\r\n            alignItems: \"center\",\r\n            minHeight: \"300px\",\r\n            flexDirection: \"column\",\r\n            gap: 2,\r\n          }}\r\n        >\r\n          <CircularProgress size={60} />\r\n          <Typography variant=\"h6\" color=\"textSecondary\">\r\n            Searching...\r\n          </Typography>\r\n        </Box>\r\n      ) : searchResults.length > 0 ? (\r\n        <Box sx={{ mt: 5 }}>\r\n          <Box\r\n            sx={{\r\n              display: \"flex\",\r\n              justifyContent: \"center\",\r\n              gap: \"10px\",\r\n              padding: \"10px\",\r\n              marginBottom: \"30px\",\r\n            }}\r\n          >\r\n            <Tabs\r\n              value={activeTab}\r\n              onChange={handleTabChange}\r\n              aria-label=\"Account tabs\"\r\n            >\r\n              <Tab label=\"All\" />\r\n              <Tab label=\"Rated\" />\r\n            </Tabs>\r\n          </Box>\r\n          {activeTab === 0 ? (\r\n            // \"All\" tab - show organized sections\r\n            <Box>\r\n              {ratedResults.length > 0 && (\r\n                <Box sx={{ mb: 4 }}>\r\n                  <Typography\r\n                    variant=\"h5\"\r\n                    component=\"h2\"\r\n                    sx={{\r\n                      mb: 1,\r\n                      fontWeight: 600,\r\n                      color: \"primary.main\",\r\n                    }}\r\n                  >\r\n                    Top Rated Accounts\r\n                  </Typography>\r\n                  <Typography\r\n                    variant=\"body2\"\r\n                    color=\"text.secondary\"\r\n                    sx={{ mb: 3 }}\r\n                  >\r\n                    Accounts sorted by their ratings from highest to lowest\r\n                  </Typography>\r\n                  <Grid container spacing={3}>\r\n                    {ratedResults\r\n                      .slice(0, Math.min(displayCount, ratedResults.length))\r\n                      .map((result) => (\r\n                        <ResultCard\r\n                          key={result.id}\r\n                          result={result}\r\n                          GetCategoryIcon={GetCategoryIcon}\r\n                        />\r\n                      ))}\r\n                  </Grid>\r\n                </Box>\r\n              )}\r\n\r\n              {normalResults.length > 0 &&\r\n                displayCount > ratedResults.length && (\r\n                  <Box sx={{ mb: 4 }}>\r\n                    <Typography\r\n                      variant=\"h5\"\r\n                      component=\"h2\"\r\n                      sx={{\r\n                        mb: 1,\r\n                        fontWeight: 600,\r\n                        color: \"primary.main\",\r\n                      }}\r\n                    >\r\n                      Other Accounts\r\n                    </Typography>\r\n                    <Typography\r\n                      variant=\"body2\"\r\n                      color=\"text.secondary\"\r\n                      sx={{ mb: 3 }}\r\n                    >\r\n                      Accounts sorted alphabetically by username\r\n                    </Typography>\r\n                    <Grid container spacing={3}>\r\n                      {normalResults\r\n                        .slice(\r\n                          0,\r\n                          Math.max(0, displayCount - ratedResults.length)\r\n                        )\r\n                        .map((result) => (\r\n                          <ResultCard\r\n                            key={result.id}\r\n                            result={result}\r\n                            GetCategoryIcon={GetCategoryIcon}\r\n                          />\r\n                        ))}\r\n                    </Grid>\r\n                  </Box>\r\n                )}\r\n            </Box>\r\n          ) : (\r\n            // \"Rated\" tab - show only rated accounts\r\n            <Grid container spacing={3}>\r\n              {sortedResults.slice(0, displayCount).map((result) => (\r\n                <ResultCard\r\n                  key={result.id}\r\n                  result={result}\r\n                  GetCategoryIcon={GetCategoryIcon}\r\n                />\r\n              ))}\r\n            </Grid>\r\n          )}\r\n          {displayCount < sortedResults.length && (\r\n            <Box\r\n              sx={{\r\n                display: \"flex\",\r\n                justifyContent: \"center\",\r\n                mt: 3,\r\n              }}\r\n            >\r\n              <Button\r\n                variant=\"outlined\"\r\n                onClick={() => setDisplayCount(displayCount + 5)}\r\n              >\r\n                View More\r\n              </Button>\r\n            </Box>\r\n          )}\r\n        </Box>\r\n      ) : (\r\n        <SearchNotFound searchQuery={searchParams.get(\"q\")} />\r\n      )}\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default SearchResults;\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\profileUser.js", ["654", "655", "656", "657", "658", "659", "660", "661", "662", "663", "664", "665"], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\LoginPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\Profile.js", ["666", "667", "668", "669", "670", "671", "672"], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\RatingPage.js", ["673", "674", "675", "676", "677", "678", "679", "680", "681", "682"], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Coupons\\AppManageCoupons.js", ["683"], [], "import { useState } from \"react\";\r\nimport { Helmet } from \"react-helmet-async\";\r\nimport { ToastContainer, toast } from \"react-toastify\";\r\nimport { Grid } from \"@mui/material\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { DataGrid } from \"@mui/x-data-grid\";\r\nimport CheckoutReserved from \"./CheckoutReserved\";\r\nimport {\r\n  Card,\r\n  Box,\r\n  CardHeader,\r\n  CardContent,\r\n  useTheme,\r\n  Container,\r\n  Typography,\r\n  Stack,\r\n  Avatar,\r\n  Button,\r\n} from \"@mui/material\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\n\r\nimport { GetCustomerCoupons, ReserveCoupon } from \"../../../CouponsData.ts\";\r\nimport { useEffect } from \"react\";\r\nimport { styled } from \"@mui/material/styles\";\r\n\r\nimport SimpleBar from \"simplebar-react\";\r\n\r\nconst Scrollbar = styled(SimpleBar)``;\r\n\r\nconst AvailableTableColumns = [\r\n  {\r\n    field: \"ownerDetails\",\r\n    headerName: \"Owner\",\r\n    width: 250,\r\n    renderCell: (params) => (\r\n      <Stack\r\n        direction=\"row\"\r\n        alignItems=\"center\"\r\n        spacing={2}\r\n        onClick={() => {\r\n          window.open(`/Profile/${params.row.ownerUserName}`, \"_blank\");\r\n        }}\r\n      >\r\n        <Avatar\r\n          alt={params.row.ownerFirstName}\r\n          src={params.row.ownerProfilePicture}\r\n        />\r\n        <div>\r\n          <Typography variant=\"subtitle2\">\r\n            {params.row.ownerFirstName} {params.row.ownerLastName}\r\n          </Typography>\r\n          <Typography\r\n            variant=\"caption\"\r\n            sx={{\r\n              color: \"text.secondary\",\r\n              mt: 0.5,\r\n              display: \"block\",\r\n            }}\r\n          >\r\n            {params.row.ownerUserName}\r\n          </Typography>\r\n        </div>\r\n      </Stack>\r\n    ),\r\n  },\r\n  // { field: \"reference\", headerName: \"Reference\", width: 140 },\r\n  { field: \"serialKey\", headerName: \"Serial Key\", width: 170 },\r\n];\r\n\r\nexport const AppManageCoupons = () => {\r\n  const [CustomerReservedCoupons, setCustomerReservedCoupons] = useState([]);\r\n  const [CustomerUsedCoupons, setCustomerUsedCoupons] = useState([]);\r\n  const [isDialogOpen, setIsDialogOpen] = useState(false);\r\n  const navigate = useNavigate();\r\n\r\n  useEffect(() => {\r\n    fetchCustomerCoupons();\r\n  }, []);\r\n\r\n  const [pagination, setPagination] = useState({\r\n    page: 0,\r\n    pageSize: 5,\r\n  });\r\n\r\n  const handleOpenDialog = () => {\r\n    setIsDialogOpen(true);\r\n  };\r\n\r\n  const handleCloseDialog = () => {\r\n    setIsDialogOpen(false);\r\n  };\r\n\r\n  const handleApplyCoupon = async (coupon) => {\r\n    try {\r\n      // Input validation\r\n      if (!coupon || typeof coupon !== \"string\" || coupon.trim() === \"\") {\r\n        throw new Error(\"Please enter a valid coupon serial key.\");\r\n      }\r\n\r\n      const response = await ReserveCoupon(coupon.trim());\r\n\r\n      if (response.error) {\r\n        throw new Error(response.error);\r\n      }\r\n\r\n      // Success - refresh data\r\n      fetchCustomerCoupons();\r\n\r\n      // Close dialog\r\n      setIsDialogOpen(false);\r\n\r\n      // Show success message\r\n      const successMessage =\r\n        response.data?.message || \"Coupon reserved successfully!\";\r\n      toast.success(successMessage, {\r\n        position: \"top-center\",\r\n        autoClose: 2000,\r\n      });\r\n    } catch (error) {\r\n      const errorMessage =\r\n        error.message || \"Failed to reserve coupon. Please try again.\";\r\n      toast.error(errorMessage, {\r\n        position: \"top-center\",\r\n        autoClose: 3000,\r\n      });\r\n    }\r\n  };\r\n\r\n  const fetchCustomerCoupons = async () => {\r\n    try {\r\n      const response = await GetCustomerCoupons();\r\n\r\n      const CustomerReservedCoupons = response.filter(\r\n        (coupon) => coupon.isReserved && !coupon.isUsed\r\n      );\r\n      const CustomerUsedCoupons = response.filter(\r\n        (coupon) => coupon.isReserved && coupon.isUsed\r\n      );\r\n\r\n      if (CustomerReservedCoupons.length > 0) {\r\n        setCustomerReservedCoupons(CustomerReservedCoupons);\r\n      }\r\n      if (CustomerUsedCoupons.length > 0) {\r\n        setCustomerUsedCoupons(CustomerUsedCoupons);\r\n      }\r\n    } catch (error) {\r\n      console.log(error.message);\r\n    }\r\n  };\r\n\r\n  const theme = useTheme();\r\n\r\n  return (\r\n    <Container>\r\n      <Helmet>\r\n        <title> IDigics | Manage Coupons </title>\r\n      </Helmet>\r\n      <Typography variant=\"h5\" sx={{ mb: 4 }}>\r\n        Manage Coupons\r\n      </Typography>\r\n      <Grid container spacing={3}>\r\n        <Grid item xs={12} sm={12} md={8}>\r\n          <Card>\r\n            <CardHeader\r\n              title=\"Your Reserved Coupons\"\r\n              subheader=\"Craft custom links to suit your needs You can easily add, edit, and delete links, logos, and URLs according to your preferences.\"\r\n            />\r\n            <CardContent>\r\n              <Scrollbar>\r\n                <Box sx={{ height: 370 }}>\r\n                  <DataGrid\r\n                    columns={AvailableTableColumns}\r\n                    rows={CustomerReservedCoupons}\r\n                    onRowClick={(o) => {\r\n                      localStorage.setItem(\"serialKey\", o.row.serialKey);\r\n                      navigate(`/Profile/${o.row.ownerUserName}`);\r\n                    }}\r\n                    sx={{\r\n                      \"& .MuiDataGrid-cell:focus\": {\r\n                        outline: \"none\",\r\n                      },\r\n                      \"& .MuiDataGrid-cell\": {\r\n                        alignContent: \"center\",\r\n                      },\r\n                      border: \"none\",\r\n                      backgroundColor: theme.palette.common.white,\r\n                    }}\r\n                    paginationModel={pagination}\r\n                    paginationMode=\"client\"\r\n                    onPaginationModelChange={setPagination}\r\n                  />\r\n                </Box>\r\n              </Scrollbar>\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n        <Grid item xs={12} sm={12} md={4}>\r\n          <Card>\r\n            <CardHeader\r\n              title=\"Reserve Coupon\"\r\n              subheader=\"Apply a coupon code to reserve it for your use\"\r\n            />\r\n            <CardContent>\r\n              <Button\r\n                fullWidth\r\n                variant=\"contained\"\r\n                color=\"primary\"\r\n                startIcon={<AddIcon />}\r\n                onClick={handleOpenDialog}\r\n                sx={{ py: 2 }}\r\n              >\r\n                Add Coupon\r\n              </Button>\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n        <ToastContainer />\r\n      </Grid>\r\n\r\n      {/* Coupon Dialog */}\r\n      {isDialogOpen && (\r\n        <CheckoutReserved\r\n          onApply={handleApplyCoupon}\r\n          ShowCouponSection={true}\r\n          onClose={handleCloseDialog}\r\n        />\r\n      )}\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default AppManageCoupons;\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\customShadows.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\globalStyles.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\typography.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\palette.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\shadows.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\layouts\\dashboard\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\chart\\useChart.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\scroll-to-top\\ScrollToTop.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\chart\\styles.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\Api.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\AnalyticsData.ts", ["684"], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\LinkData.ts", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\PurchasesData.ts", [], [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\Context\\config.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\Logo.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\SearchNotFound.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\PageNotFoundIllustration.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\SeverErrorIllustration.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\overrides\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\Appearance.js", ["685", "686", "687", "688", "689", "690", "691", "692", "693"], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\hooks\\useResponsive.js", [], ["694"], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Coupons\\EmptyContent.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\CouponsData.ts", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\RatingDialog.js", ["695"], [], "import React, { useState } from \"react\";\r\nimport {\r\n    <PERSON><PERSON>,\r\n    <PERSON>alog<PERSON><PERSON>nt,\r\n    Button,\r\n    Box,\r\n    Paper,\r\n    <PERSON>per,\r\n    StepLabel,\r\n    Typography,\r\n    Step,\r\n    StepContent,\r\n    TextField,\r\n    Grid,\r\n    IconButton,\r\n} from \"@mui/material\";\r\nimport ThankYouCard from \"../sections/@dashboard/Rating/ThankYouCard\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\n\r\nconst Steps = [\r\n    {\r\n        label: \"Provide the coupon please\",\r\n        description:\r\n            \"Can you please provide the coupon serial key to perform this rating\",\r\n    },\r\n    {\r\n        label: \"Quality of work\",\r\n        description:\r\n            \"Evaluate the overall quality and accuracy of the work performed by the individual.\",\r\n    },\r\n    {\r\n        label: \"Communication\",\r\n        description:\r\n            \"Assess the effectiveness of communication between you and the individual.\",\r\n    },\r\n    {\r\n        label: \"Timeliness\",\r\n        description:\r\n            \"Consider the punctuality and adherence to deadlines in completing tasks.\",\r\n    },\r\n    {\r\n        label: \"Cost-Effectiveness\",\r\n        description:\r\n            \"Review the efficiency and value for money of the services provided.\",\r\n    },\r\n    {\r\n        label: \"Agility\",\r\n        description:\r\n            \"Examine whether the individual conducts themselves with integrity and adherence to ethical principles.\",\r\n    },\r\n];\r\n\r\nexport default function RatingDialog({\r\n    serialKey,\r\n    openDialog,\r\n    onClose,\r\n    onClick,\r\n}) {\r\n    const [feedbackScores, setFeedbackScores] = useState(\r\n        Array(Steps.length).fill(0)\r\n    );\r\n    const [Skill_QualityOfWork, setSkill_QualityOfWork] = useState(0);\r\n    const [Skill_CostEffectiveness, setSkill_CostEffectiveness] = useState(0);\r\n    const [Skill_Timeliness, setSkill_Timeliness] = useState(0);\r\n    const [Skill_Communication, setSkill_Communication] = useState(0);\r\n    const [Skill_Agility, setSkill_Agility] = useState(0);\r\n\r\n    const [SerialKey, setSerialKey] = useState(serialKey ? serialKey : \"\");\r\n\r\n    const handleRatingClick = (score, step) => {\r\n        const updatedFeedbackScores = [...feedbackScores];\r\n        updatedFeedbackScores[step] = score;\r\n        setFeedbackScores(updatedFeedbackScores);\r\n\r\n        switch (step) {\r\n            case 1:\r\n                setSkill_QualityOfWork(score);\r\n                break;\r\n            case 2:\r\n                setSkill_CostEffectiveness(score);\r\n                break;\r\n            case 3:\r\n                setSkill_Timeliness(score);\r\n                break;\r\n            case 4:\r\n                setSkill_Communication(score);\r\n                break;\r\n            case 5:\r\n                setSkill_Agility(score);\r\n                break;\r\n        }\r\n    };\r\n\r\n    const [activeStep, setActiveStep] = useState(SerialKey ? 1 : 0);\r\n\r\n    const handleFinish = async () => {\r\n        onClick({\r\n            SerialKey,\r\n            Skill_QualityOfWork,\r\n            Skill_CostEffectiveness,\r\n            Skill_Timeliness,\r\n            Skill_Communication,\r\n            Skill_Agility,\r\n        });\r\n        onClose();\r\n    };\r\n\r\n    const handleClose = () => {\r\n        onClose();\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <Dialog open={openDialog} fullWidth maxWidth=\"sm\">\r\n                <DialogContent>\r\n                    <IconButton\r\n                        onClick={handleClose}\r\n                        sx={{\r\n                            position: \"absolute\",\r\n                            top: 0,\r\n                            right: 0,\r\n                        }}\r\n                    >\r\n                        <CloseIcon />\r\n                    </IconButton>\r\n\r\n                    {activeStep === Steps.length ? (\r\n                        <Paper sx={{ p: 3, mt: 3, bgcolor: \"grey.50012\" }}>\r\n                            <ThankYouCard\r\n                                feedbackScores={[\r\n                                    Skill_QualityOfWork,\r\n                                    Skill_CostEffectiveness,\r\n                                    Skill_Timeliness,\r\n                                    Skill_Communication,\r\n                                    Skill_Agility,\r\n                                ]}\r\n                            />\r\n                            <Button variant=\"contained\" onClick={handleFinish}>\r\n                                Confirm\r\n                            </Button>\r\n                            <Button\r\n                                onClick={() => {\r\n                                    setActiveStep(\r\n                                        (prevActiveStep) => prevActiveStep - 1\r\n                                    );\r\n                                }}\r\n                            >\r\n                                Back\r\n                            </Button>\r\n                        </Paper>\r\n                    ) : (\r\n                        <Stepper activeStep={activeStep} orientation=\"vertical\">\r\n                            {Steps.map((step, index) => (\r\n                                <Step key={step.label}>\r\n                                    <StepLabel\r\n                                        optional={\r\n                                            index === Steps.length - 1 ? (\r\n                                                <Typography variant=\"caption\">\r\n                                                    Last step\r\n                                                </Typography>\r\n                                            ) : null\r\n                                        }\r\n                                    >\r\n                                        {step.label}\r\n                                    </StepLabel>\r\n                                    <StepContent>\r\n                                        {index === 0 ? (\r\n                                            <Grid\r\n                                                container\r\n                                                spacing={2}\r\n                                                alignItems=\"center\"\r\n                                            >\r\n                                                <Grid item xs={12} sm={6}>\r\n                                                    <TextField\r\n                                                        label=\"Coupon Serial Key\"\r\n                                                        value={SerialKey}\r\n                                                        onChange={(event) =>\r\n                                                            setSerialKey(\r\n                                                                event.target\r\n                                                                    .value\r\n                                                            )\r\n                                                        }\r\n                                                        fullWidth\r\n                                                    />\r\n                                                </Grid>\r\n                                                <Grid\r\n                                                    item\r\n                                                    xs={12}\r\n                                                    sm={6}\r\n                                                    container\r\n                                                    justifyContent=\"flex-end\"\r\n                                                >\r\n                                                    <Button\r\n                                                        variant=\"contained\"\r\n                                                        color=\"primary\"\r\n                                                        onClick={() =>\r\n                                                            setActiveStep(\r\n                                                                (\r\n                                                                    prevActiveStep\r\n                                                                ) =>\r\n                                                                    prevActiveStep +\r\n                                                                    1\r\n                                                            )\r\n                                                        }\r\n                                                        disabled={\r\n                                                            SerialKey === \"\"\r\n                                                        }\r\n                                                    >\r\n                                                        Next\r\n                                                    </Button>\r\n                                                </Grid>\r\n                                            </Grid>\r\n                                        ) : (\r\n                                            <>\r\n                                                <Typography>\r\n                                                    {step.description}\r\n                                                </Typography>\r\n                                                <Box sx={{ mt: 3 }}>\r\n                                                    <Grid container spacing={2}>\r\n                                                        <Grid item xs={12}>\r\n                                                            <Grid\r\n                                                                container\r\n                                                                justifyContent=\"center\"\r\n                                                                spacing={2}\r\n                                                            >\r\n                                                                {[\r\n                                                                    1, 2, 3, 4,\r\n                                                                    5,\r\n                                                                ].map(\r\n                                                                    (score) => (\r\n                                                                        <Grid\r\n                                                                            item\r\n                                                                            key={\r\n                                                                                score\r\n                                                                            }\r\n                                                                        >\r\n                                                                            <Button\r\n                                                                                onClick={() => {\r\n                                                                                    handleRatingClick(\r\n                                                                                        score,\r\n                                                                                        activeStep\r\n                                                                                    );\r\n                                                                                    setActiveStep(\r\n                                                                                        (\r\n                                                                                            prevActiveStep\r\n                                                                                        ) =>\r\n                                                                                            prevActiveStep +\r\n                                                                                            1\r\n                                                                                    );\r\n                                                                                }}\r\n                                                                                sx={{\r\n                                                                                    bgcolor:\r\n                                                                                        feedbackScores[\r\n                                                                                            activeStep\r\n                                                                                        ] ===\r\n                                                                                        score\r\n                                                                                            ? \"primary.main\"\r\n                                                                                            : \"grey.300\",\r\n                                                                                    color:\r\n                                                                                        feedbackScores[\r\n                                                                                            activeStep\r\n                                                                                        ] ===\r\n                                                                                        score\r\n                                                                                            ? \"common.white\"\r\n                                                                                            : \"grey.800\",\r\n                                                                                    \"&:hover\":\r\n                                                                                        {\r\n                                                                                            bgcolor:\r\n                                                                                                \"primary.dark\",\r\n                                                                                            color: \"common.white\",\r\n                                                                                        },\r\n                                                                                    borderRadius:\r\n                                                                                        \"50%\",\r\n                                                                                    minWidth:\r\n                                                                                        \"50px\",\r\n                                                                                    height: \"50px\",\r\n                                                                                    fontSize:\r\n                                                                                        \"18px\",\r\n                                                                                    fontWeight: 600,\r\n                                                                                }}\r\n                                                                            >\r\n                                                                                {\r\n                                                                                    score\r\n                                                                                }\r\n                                                                            </Button>\r\n                                                                        </Grid>\r\n                                                                    )\r\n                                                                )}\r\n                                                            </Grid>\r\n                                                        </Grid>\r\n                                                    </Grid>\r\n                                                    <Button\r\n                                                        disabled={index === 0}\r\n                                                        onClick={() => {\r\n                                                            setActiveStep(\r\n                                                                (\r\n                                                                    prevActiveStep\r\n                                                                ) =>\r\n                                                                    prevActiveStep -\r\n                                                                    1\r\n                                                            );\r\n                                                        }}\r\n                                                    >\r\n                                                        Back\r\n                                                    </Button>\r\n                                                </Box>\r\n                                            </>\r\n                                        )}\r\n                                    </StepContent>\r\n                                </Step>\r\n                            ))}\r\n                        </Stepper>\r\n                    )}\r\n                </DialogContent>\r\n            </Dialog>\r\n        </>\r\n    );\r\n}\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppWebsiteVisits.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppCurrentVisits.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppConversionRates.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppDepositsTab.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppTasks.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppPurchasesTab.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppLinksByProfile.js", ["696", "697", "698", "699", "700", "701"], [], "import PropTypes from \"prop-types\";\r\nimport { Box, Paper, Typography, Avatar } from \"@mui/material\";\r\n\r\nimport AutoFixHighIcon from \"@mui/icons-material/AutoFixHigh\";\r\nimport DeleteIcon from \"@mui/icons-material/Delete\";\r\nimport EmptyContent from \"../Coupons/EmptyContent\";\r\n\r\nAppLinksByProfile.propTypes = {\r\n  title: PropTypes.string,\r\n  subheader: PropTypes.string,\r\n  list: PropTypes.array.isRequired,\r\n};\r\n\r\nexport default function AppLinksByProfile({\r\n  type,\r\n  list,\r\n  onDelete,\r\n  onEdit,\r\n  ProfileCardVisible,\r\n}) {\r\n  const handleEdit = (link) => {\r\n    if (ProfileCardVisible) ProfileCardVisible(false);\r\n    onEdit(link);\r\n  };\r\n  return (\r\n    <Box sx={{ width: \"100%\", maxWidth: \"100%\", overflow: \"hidden\" }}>\r\n      {list.length == 0 ? (\r\n        <EmptyContent\r\n          description={`Looks like you have no ${type}  yet.`}\r\n          img=\"/assets/illustrations/illustration_empty_content.svg\"\r\n        />\r\n      ) : type == \"socialLinks\" ? (\r\n        list.map((link, Id) => (\r\n          <Paper\r\n            variant=\"outlined\"\r\n            sx={{\r\n              padding: \"0.8rem 1.5rem\",\r\n              position: \"relative\",\r\n              borderRadius: \"10px\",\r\n              cursor: \"pointer\",\r\n              display: \"flex\",\r\n              justifyContent: \"space-between\",\r\n              alignItems: \"center\",\r\n              margin: \"0.6rem 0\",\r\n              marginTop: \"15px\",\r\n              boxShadow: \"0.1rem 0.2rem 0.2rem rgba(80, 40, 10, 0.2)\",\r\n              transition: \"transform 0.3s ease-in-out\",\r\n              width: \"100%\",\r\n              maxWidth: \"100%\",\r\n              minWidth: 0,\r\n              overflow: \"hidden\",\r\n            }}\r\n            key={Id}\r\n          >\r\n            {/* Left side: Icon and Title */}\r\n            <Box\r\n              sx={{\r\n                display: \"flex\",\r\n                alignItems: \"center\",\r\n                flex: 1,\r\n                minWidth: 0, // Allow text to truncate if needed\r\n              }}\r\n            >\r\n              <Box\r\n                color={link.Color}\r\n                fontSize=\"18px\"\r\n                sx={{\r\n                  marginRight: \"12px\",\r\n                  flexShrink: 0, // Prevent icon from shrinking\r\n                }}\r\n              >\r\n                {link.Icon}\r\n              </Box>\r\n\r\n              <Typography\r\n                sx={{\r\n                  color: \"rgba(20, 43, 58, 1)\",\r\n                  fontWeight: 600,\r\n                  overflow: \"hidden\",\r\n                  textOverflow: \"ellipsis\",\r\n                  whiteSpace: \"nowrap\",\r\n                  flex: 1,\r\n                }}\r\n              >\r\n                {link.Title}\r\n              </Typography>\r\n            </Box>\r\n\r\n            {/* Right side: Action buttons */}\r\n            <Box\r\n              sx={{\r\n                display: \"flex\",\r\n                alignItems: \"center\",\r\n                flexShrink: 0, // Prevent buttons from shrinking\r\n              }}\r\n            >\r\n              <a onClick={() => handleEdit(link)}>\r\n                <AutoFixHighIcon\r\n                  sx={{\r\n                    marginRight: \"10px\",\r\n                    cursor: \"pointer\",\r\n                    \"&:hover\": {\r\n                      color: \"#ff715b\",\r\n                      fontSize: \"27px\",\r\n                    },\r\n                  }}\r\n                />\r\n              </a>\r\n              <a onClick={() => onDelete(link.Id)}>\r\n                <DeleteIcon\r\n                  sx={{\r\n                    cursor: \"pointer\",\r\n                    \"&:hover\": {\r\n                      color: \"#ff715b\",\r\n                      fontSize: \"27px\",\r\n                    },\r\n                  }}\r\n                />\r\n              </a>\r\n            </Box>\r\n          </Paper>\r\n        ))\r\n      ) : (\r\n        list.map((link, Id) => (\r\n          <Paper\r\n            sx={{\r\n              padding: \"0.8rem 1.5rem\",\r\n              position: \"relative\",\r\n              borderRadius: \"10px\",\r\n              cursor: \"pointer\",\r\n              display: \"flex\",\r\n              justifyContent: \"space-between\",\r\n              alignItems: \"center\",\r\n              margin: \"0.6rem 0\",\r\n              marginTop: \"15px\",\r\n              boxShadow: \"0.1rem 0.2rem 0.2rem rgba(80, 40, 10, 0.2)\",\r\n              transition: \"transform 0.3s ease-in-out\",\r\n              width: \"100%\",\r\n              maxWidth: \"100%\",\r\n              minWidth: 0, // Allow flex items to shrink\r\n              overflow: \"hidden\",\r\n              \"&::after\": {\r\n                content: '\"\"',\r\n                position: \"absolute\",\r\n                top: 0,\r\n                right: 0,\r\n                bottom: 0,\r\n                width: \"5px\",\r\n                backgroundColor: \"#ff715b\",\r\n                borderTopRightRadius: \"10px\",\r\n                borderBottomRightRadius: \"10px\",\r\n              },\r\n            }}\r\n            key={Id}\r\n          >\r\n            {/* Left side: Icon and Title */}\r\n            <Box\r\n              sx={{\r\n                display: \"flex\",\r\n                alignItems: \"center\",\r\n                flex: 1,\r\n                minWidth: 0, // Allow text to truncate if needed\r\n              }}\r\n            >\r\n              {type === \"contactLinks\" ? (\r\n                <Box\r\n                  sx={{\r\n                    width: \"30px\",\r\n                    height: \"30px\",\r\n                    borderRadius: \"50%\",\r\n                    display: \"flex\",\r\n                    alignItems: \"center\",\r\n                    justifyContent: \"center\",\r\n                    backgroundColor: link.Color,\r\n                    marginRight: \"12px\",\r\n                    flexShrink: 0,\r\n                    boxShadow: \"0 0.4rem 0.8rem rgba(20, 43, 58, 0.25)\",\r\n                  }}\r\n                >\r\n                  <i\r\n                    className={link.Icon}\r\n                    style={{\r\n                      color: \"white\",\r\n                      fontSize: \"14px\",\r\n                    }}\r\n                  />\r\n                </Box>\r\n              ) : (\r\n                <Avatar\r\n                  style={{\r\n                    width: \"30px\",\r\n                    height: \"30px\",\r\n                    borderRadius: \"60%\",\r\n                    boxShadow: \"0 0.4rem 0.8rem rgba(20, 43, 58, 0.25)\",\r\n                    marginRight: \"12px\",\r\n                    flexShrink: 0, // Prevent avatar from shrinking\r\n                  }}\r\n                  src={link.Icon}\r\n                  alt=\"User Profile Photo\"\r\n                />\r\n              )}\r\n              <Box sx={{ flex: 1, minWidth: 0 }}>\r\n                <Typography\r\n                  sx={{\r\n                    color: \"rgba(20, 43, 58, 1)\",\r\n                    fontWeight: 600,\r\n                    fontSize: \"15px\",\r\n                    overflow: \"hidden\",\r\n                    textOverflow: \"ellipsis\",\r\n                    whiteSpace: \"nowrap\",\r\n                  }}\r\n                >\r\n                  {link.Title}\r\n                </Typography>\r\n                {type === \"contactLinks\" && link.ContactInfo && link.Name && (\r\n                  <Typography\r\n                    sx={{\r\n                      color: \"rgba(20, 43, 58, 0.7)\",\r\n                      fontSize: \"13px\",\r\n                      overflow: \"hidden\",\r\n                      textOverflow: \"ellipsis\",\r\n                      whiteSpace: \"nowrap\",\r\n                    }}\r\n                  >\r\n                    {link.ContactInfo}\r\n                  </Typography>\r\n                )}\r\n              </Box>\r\n            </Box>\r\n\r\n            {/* Right side: Action buttons */}\r\n            <Box\r\n              sx={{\r\n                display: \"flex\",\r\n                alignItems: \"center\",\r\n                flexShrink: 0, // Prevent buttons from shrinking\r\n              }}\r\n            >\r\n              <a onClick={() => handleEdit(link)}>\r\n                <AutoFixHighIcon\r\n                  sx={{\r\n                    marginRight: \"10px\",\r\n                    cursor: \"pointer\",\r\n                    \"&:hover\": {\r\n                      color: \"#ff715b\",\r\n                      fontSize: \"27px\",\r\n                    },\r\n                  }}\r\n                />\r\n              </a>\r\n              <a onClick={() => onDelete(link.Id)}>\r\n                <DeleteIcon\r\n                  sx={{\r\n                    cursor: \"pointer\",\r\n                    \"&:hover\": {\r\n                      color: \"#ff715b\",\r\n                      fontSize: \"27px\",\r\n                    },\r\n                  }}\r\n                />\r\n              </a>\r\n            </Box>\r\n          </Paper>\r\n        ))\r\n      )}\r\n    </Box>\r\n  );\r\n}\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppWidgetSummary.js", ["702"], [], "// @mui\r\nimport PropTypes from \"prop-types\";\r\nimport { alpha, styled } from \"@mui/material/styles\";\r\nimport { Card, Typography } from \"@mui/material\";\r\n// utils\r\nimport { fShortenNumber } from \"../../../utils/formatNumber\";\r\n// components\r\nimport Iconify from \"../../../components/iconify\";\r\n\r\n// ----------------------------------------------------------------------\r\n\r\nconst StyledIcon = styled(\"div\")(({ theme }) => ({\r\n    margin: \"auto\",\r\n    display: \"flex\",\r\n    borderRadius: \"50%\",\r\n    alignItems: \"center\",\r\n    width: theme.spacing(8),\r\n    height: theme.spacing(8),\r\n    justifyContent: \"center\",\r\n    marginBottom: theme.spacing(3),\r\n}));\r\n\r\n// ----------------------------------------------------------------------\r\n\r\nAppWidgetSummary.propTypes = {\r\n    color: PropTypes.string,\r\n    icon: PropTypes.string,\r\n    title: PropTypes.string.isRequired,\r\n    total: PropTypes.number.isRequired,\r\n    sx: PropTypes.object,\r\n};\r\n\r\nexport default function AppWidgetSummary({\r\n    title,\r\n    total,\r\n    icon,\r\n    color = \"primary\",\r\n    sx,\r\n    ...other\r\n}) {\r\n    return (\r\n        <Card\r\n            sx={{\r\n                py: 5,\r\n                boxShadow: 0,\r\n                textAlign: \"center\",\r\n                color: (theme) => theme.palette[color].darker,\r\n                bgcolor: (theme) => theme.palette[color].lighter,\r\n                ...sx,\r\n            }}\r\n            {...other}\r\n        >\r\n            <StyledIcon\r\n                sx={{\r\n                    color: (theme) => theme.palette[color].dark,\r\n                    backgroundImage: (theme) =>\r\n                        `linear-gradient(135deg, ${alpha(\r\n                            theme.palette[color].dark,\r\n                            0\r\n                        )} 0%, ${alpha(theme.palette[color].dark, 0.24)} 100%)`,\r\n                }}\r\n            >\r\n                <Iconify icon={icon} width={24} height={24} />\r\n            </StyledIcon>\r\n\r\n            <Typography variant=\"h3\">{total}</Typography>\r\n\r\n            <Typography variant=\"subtitle2\" sx={{ opacity: 0.72 }}>\r\n                {title}\r\n            </Typography>\r\n        </Card>\r\n    );\r\n}\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\utils\\formatTime.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\pages\\ProfileAbout.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\InviterFriends.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Coupons\\CheckoutReserved.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Account\\AccountSettings.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Rating\\BookingCustomerReviews.js", ["703", "704"], [], "import PropTypes from \"prop-types\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { GetCouponsFromCustomers } from \"../../../CouponsData.ts\";\r\nimport Tooltip, { tooltipClasses } from \"@mui/material/Tooltip\";\r\nimport { toast } from \"react-toastify\";\r\n// material\r\nimport { useTheme } from \"@mui/material/styles\";\r\nimport {\r\n    Card,\r\n    Chip,\r\n    Stack,\r\n    Avatar,\r\n    Rating,\r\n    Button,\r\n    CardHeader,\r\n    Typography,\r\n    MobileStepper,\r\n} from \"@mui/material\";\r\n// import SwipeableViews from \"react-swipeable-views\";\r\n// utils\r\nimport { fDateTime } from \"../../../utils/formatTime\";\r\nimport { styled } from \"@mui/material/styles\";\r\n\r\nconst BootstrapTooltip = styled(({ className, ...props }) => (\r\n    <Tooltip {...props} arrow classes={{ popper: className }} />\r\n))(({ theme }) => ({\r\n    [`& .${tooltipClasses.arrow}`]: {\r\n        color: \"#ee705e\",\r\n    },\r\n    [`& .${tooltipClasses.tooltip}`]: {\r\n        backgroundColor: \"#ee705e\",\r\n    },\r\n}));\r\n// ----------------------------------------------------------------------\r\n\r\nReviewItem.propTypes = {\r\n    item: PropTypes.shape({\r\n        avatar: PropTypes.string,\r\n        description: PropTypes.string,\r\n        name: PropTypes.string,\r\n        postedAt: PropTypes.instanceOf(Date),\r\n        rating: PropTypes.number,\r\n        tags: PropTypes.arrayOf(PropTypes.string),\r\n    }),\r\n};\r\n\r\nfunction ReviewItem({ customer }) {\r\n    const {\r\n        profilePicture,\r\n        firstName,\r\n        lastName,\r\n        skill_QualityOfWork,\r\n        skill_CostEffectiveness,\r\n        skill_Timeliness,\r\n        skill_Communication,\r\n        skill_Agility,\r\n        useDate,\r\n    } = customer;\r\n\r\n    const openNewTab = (url) => {\r\n        window.open(url, \"_blank\");\r\n    };\r\n\r\n    return (\r\n        <Stack spacing={2} sx={{ position: \"relative\", p: 3 }}>\r\n            <Stack\r\n                direction=\"row\"\r\n                alignItems=\"center\"\r\n                spacing={2}\r\n                onClick={() => {\r\n                    openNewTab(`/Profile/${customer.profile.userName}`);\r\n                }}\r\n            >\r\n                <Avatar alt={firstName} src={profilePicture} />\r\n                <div>\r\n                    <Typography variant=\"subtitle2\">\r\n                        {firstName} {lastName}\r\n                    </Typography>\r\n                    <Typography\r\n                        variant=\"caption\"\r\n                        sx={{\r\n                            color: \"text.secondary\",\r\n                            mt: 0.5,\r\n                            display: \"block\",\r\n                        }}\r\n                    >\r\n                        Posted {fDateTime(useDate)}\r\n                    </Typography>\r\n                </div>\r\n            </Stack>\r\n\r\n            <BootstrapTooltip\r\n                title={\r\n                    \"assess the output/ service and whether it meets your expectations\"\r\n                }\r\n                sx={{\r\n                    \"& .MuiTooltip-tooltip\": {\r\n                        fontSize: \"13px\",\r\n                    },\r\n                }}\r\n            >\r\n                <Stack direction=\"row\" alignItems=\"center\" spacing={5}>\r\n                    <Typography\r\n                        variant=\"body2\"\r\n                        sx={{\r\n                            display: \"flex\",\r\n                            width: 125,\r\n                            justifyContent: \"left\",\r\n                        }}\r\n                    >\r\n                        Quality of work\r\n                    </Typography>\r\n                    <Rating\r\n                        value={skill_QualityOfWork}\r\n                        size=\"medium\"\r\n                        readOnly\r\n                        precision={0.1}\r\n                    />\r\n                    {/* <Typography\r\n                    variant=\"subtitle3\"\r\n                    sx={{ color: \"text.secondary\", minWidth: 40 }}\r\n                >\r\n                    {formatFloatToOneDecimal(skill_QualityOfWork)}\r\n                </Typography> */}\r\n                </Stack>\r\n            </BootstrapTooltip>\r\n\r\n            <BootstrapTooltip\r\n                title={\r\n                    \"determine if the user provides value for money based on the quality of their work and pricing\"\r\n                }\r\n                sx={{\r\n                    \"& .MuiTooltip-tooltip\": {\r\n                        fontSize: \"13px\",\r\n                    },\r\n                }}\r\n            >\r\n                <Stack direction=\"row\" alignItems=\"center\" spacing={5}>\r\n                    <Typography\r\n                        variant=\"body2\"\r\n                        sx={{\r\n                            display: \"flex\",\r\n                            width: 125,\r\n                            justifyContent: \"left\",\r\n                        }}\r\n                    >\r\n                        Cost Effectiveness\r\n                    </Typography>\r\n                    <Rating\r\n                        value={skill_CostEffectiveness}\r\n                        size=\"medium\"\r\n                        readOnly\r\n                        precision={0.1}\r\n                        sx={{ display: \"flex\", justifyContent: \"left\" }}\r\n                    />\r\n                    {/* <Typography\r\n                    variant=\"subtitle3\"\r\n                    sx={{\r\n                        display: \"flex\",\r\n                        color: \"text.secondary\",\r\n                        justifyContent: \"right\",\r\n                    }}\r\n                >\r\n                    {formatFloatToOneDecimal(skill_CostEffectiveness)}\r\n                </Typography> */}\r\n                </Stack>\r\n            </BootstrapTooltip>\r\n\r\n            <BootstrapTooltip\r\n                title={\r\n                    \"consider if the user delivers work on time or within agreed-upon deadlines\"\r\n                }\r\n                sx={{\r\n                    \"& .MuiTooltip-tooltip\": {\r\n                        fontSize: \"13px\",\r\n                    },\r\n                }}\r\n            >\r\n                <Stack direction=\"row\" alignItems=\"center\" spacing={5}>\r\n                    <Typography\r\n                        variant=\"body2\"\r\n                        sx={{\r\n                            display: \"flex\",\r\n                            width: 125,\r\n                            justifyContent: \"left\",\r\n                        }}\r\n                    >\r\n                        Timeliness\r\n                    </Typography>\r\n                    <Rating\r\n                        value={skill_Timeliness}\r\n                        size=\"medium\"\r\n                        readOnly\r\n                        precision={0.1}\r\n                    />\r\n                    {/* <Typography\r\n                    variant=\"subtitle3\"\r\n                    sx={{ color: \"text.secondary\", minWidth: 40 }}\r\n                >\r\n                    {formatFloatToOneDecimal(skill_Timeliness)}\r\n                </Typography> */}\r\n                </Stack>\r\n            </BootstrapTooltip>\r\n\r\n            <BootstrapTooltip\r\n                title={\r\n                    \"evaluate how well the user communicates and understands your requirements\"\r\n                }\r\n                sx={{\r\n                    \"& .MuiTooltip-tooltip\": {\r\n                        fontSize: \"13px\",\r\n                    },\r\n                }}\r\n            >\r\n                <Stack direction=\"row\" alignItems=\"center\" spacing={5}>\r\n                    <Typography\r\n                        variant=\"body2\"\r\n                        sx={{\r\n                            display: \"flex\",\r\n                            width: 125,\r\n                            justifyContent: \"left\",\r\n                        }}\r\n                    >\r\n                        Communication\r\n                    </Typography>\r\n                    <Rating\r\n                        value={skill_Communication}\r\n                        size=\"medium\"\r\n                        readOnly\r\n                        precision={0.1}\r\n                    />\r\n                    {/* <Typography\r\n                    variant=\"subtitle3\"\r\n                    sx={{ color: \"text.secondary\", minWidth: 40 }}\r\n                >\r\n                    {formatFloatToOneDecimal(skill_Communication)}\r\n                </Typography> */}\r\n                </Stack>\r\n            </BootstrapTooltip>\r\n\r\n            <BootstrapTooltip\r\n                title={\"see if it fits working with\"}\r\n                sx={{\r\n                    \"& .MuiTooltip-tooltip\": {\r\n                        fontSize: \"13px\",\r\n                    },\r\n                }}\r\n            >\r\n                <Stack direction=\"row\" alignItems=\"center\" spacing={5}>\r\n                    <Typography\r\n                        variant=\"body2\"\r\n                        sx={{\r\n                            display: \"flex\",\r\n                            width: 125,\r\n                            justifyContent: \"left\",\r\n                        }}\r\n                    >\r\n                        Agility\r\n                    </Typography>\r\n                    <Rating\r\n                        value={skill_Agility}\r\n                        size=\"medium\"\r\n                        readOnly\r\n                        precision={0.1}\r\n                    />\r\n                    {/* <Typography\r\n                    variant=\"subtitle3\"\r\n                    sx={{ color: \"text.secondary\", minWidth: 40 }}\r\n                >\r\n                    {formatFloatToOneDecimal(skill_Agility)}\r\n                </Typography> */}\r\n                </Stack>\r\n            </BootstrapTooltip>\r\n        </Stack>\r\n    );\r\n}\r\n\r\nexport default function BookingCustomerReviews() {\r\n    const [Customers, setCustomers] = useState([]);\r\n    const theme = useTheme();\r\n    const [activeStep, setActiveStep] = useState(0);\r\n\r\n    useEffect(() => {\r\n        fetchCouponsFromCustomers();\r\n    }, []);\r\n\r\n    const fetchCouponsFromCustomers = async () => {\r\n        try {\r\n            const response = await GetCouponsFromCustomers();\r\n            if (response.error) throw new Error(response.error);\r\n            setCustomers(response.data);\r\n        } catch (error) {\r\n            toast.error(error.message, {\r\n                position: \"top-center\",\r\n                autoClose: 1000,\r\n            });\r\n        }\r\n    };\r\n\r\n    const maxSteps = Customers.length;\r\n\r\n    const handleNext = () => {\r\n        setActiveStep((prevActiveStep) => prevActiveStep + 1);\r\n    };\r\n\r\n    const handleBack = () => {\r\n        setActiveStep((prevActiveStep) => prevActiveStep - 1);\r\n    };\r\n\r\n    return (\r\n        <Card>\r\n            <CardHeader\r\n                title=\"Customer Rates\"\r\n                subheader={`${Customers.length} Rates`}\r\n                sx={{\r\n                    \"& .MuiCardHeader-action\": {\r\n                        alignSelf: \"center\",\r\n                    },\r\n                }}\r\n            />\r\n\r\n            {/* <SwipeableViews\r\n                index={activeStep}\r\n                onChangeIndex={(index) => setActiveStep(index)}\r\n                enableMouseEvents\r\n                axis={theme.direction === \"rtl\" ? \"x-reverse\" : \"x\"}\r\n            >\r\n            {Customers.map((customer, index) => (\r\n                <div key={index}>\r\n                    {Math.abs(activeStep - index) <= 2 ? (\r\n                        <ReviewItem customer={customer} />\r\n                    ) : null}\r\n                </div>\r\n            ))}\r\n        </SwipeableViews> */}\r\n            <MobileStepper\r\n                variant=\"dots\"\r\n                steps={maxSteps}\r\n                position=\"static\"\r\n                activeStep={activeStep}\r\n                nextButton={\r\n                    <Button\r\n                        size=\"small\"\r\n                        onClick={handleNext}\r\n                        disabled={activeStep === maxSteps - 1}\r\n                    >\r\n                        Next\r\n                    </Button>\r\n                }\r\n                backButton={\r\n                    <Button\r\n                        size=\"small\"\r\n                        onClick={handleBack}\r\n                        disabled={activeStep === 0}\r\n                    >\r\n                        Back\r\n                    </Button>\r\n                }\r\n                sx={{ maxWidth: 400, flexGrow: 1, mx: \"auto\" }}\r\n            />\r\n        </Card>\r\n    );\r\n}\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Rating\\RatingDetailsReviewTrack.js", ["705", "706", "707", "708"], [], "import { styled } from \"@mui/system\";\r\nimport { formatFloatToOneDecimal } from \"../../../utils/formatNumber\";\r\nimport { keyframes } from \"@emotion/react\";\r\nimport {\r\n    Grid,\r\n    Rating,\r\n    Box,\r\n    Typography,\r\n    LinearProgress,\r\n    Stack,\r\n} from \"@mui/material\";\r\n\r\n// ----------------------------------------------------------------------\r\n\r\nconst RatingStyle = styled(Rating)(({ theme }) => ({\r\n    marginBottom: \"5vh\",\r\n    fontSize: \"calc(8vh + 1rem)\",\r\n}));\r\n\r\nconst GridStyle = styled(Grid)(({ theme }) => ({\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    flexDirection: \"column\",\r\n    justifyContent: \"center\",\r\n}));\r\n\r\nconst fadeInAnimation = keyframes`\r\n  0% {\r\n    opacity: 0;\r\n  }\r\n  100% {\r\n    opacity: 1;\r\n  }\r\n`;\r\n\r\nconst bounceAnimation = keyframes`\r\n  0% {\r\n    transform: translateY(0);\r\n  }\r\n  50% {\r\n    transform: translateY(-20px);\r\n  }\r\n  100% {\r\n    transform: translateY(0);\r\n  }\r\n`;\r\n\r\nconst rotateAnimation = keyframes`\r\n  from {\r\n    transform: rotate(0deg);\r\n  }\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n`;\r\n\r\nconst pulsateAnimation = keyframes`\r\n  0% {\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    transform: scale(1.04);\r\n  }\r\n  100% {\r\n    transform: scale(1);\r\n  }\r\n`;\r\n\r\nconst glowAnimation = keyframes`\r\n  0% {\r\n    text-shadow: 0 0 5px rgba(255, 0, 0, 0.5), 0 0 10px rgba(255, 0, 0, 0.5), 0 0 15px rgba(255, 0, 0, 0.5);\r\n  }\r\n  50% {\r\n    text-shadow: 0 0 10px rgba(255, 0, 0, 0.8), 0 0 15px rgba(255, 0, 0, 0.8), 0 0 20px rgba(255, 0, 0, 0.8);\r\n  }\r\n  100% {\r\n    text-shadow: 0 0 5px rgba(255, 0, 0, 0.5), 0 0 10px rgba(255, 0, 0, 0.5), 0 0 15px rgba(255, 0, 0, 0.5);\r\n  }\r\n`;\r\n\r\nfunction ProgressItem({ star }) {\r\n    const { name, starCount } = star;\r\n    return (\r\n        <Stack direction=\"row\" alignItems=\"center\" spacing=\"3vh\">\r\n            <Typography\r\n                variant=\"subtitle2\"\r\n                sx={{\r\n                    display: \"flex\",\r\n                    width: \"35%\",\r\n                    justifyContent: \"left\",\r\n                    fontSize: \"2.8vh\",\r\n                    fontWeight: \"Bold\",\r\n                }}\r\n            >\r\n                {name}\r\n            </Typography>\r\n            <LinearProgress\r\n                variant=\"determinate\"\r\n                value={(starCount / 5) * 100}\r\n                sx={{\r\n                    flexGrow: 1,\r\n                    bgcolor: \"divider\",\r\n                    height: \"1.5vh\",\r\n                }}\r\n            />\r\n            <Typography\r\n                variant=\"subtitle3\"\r\n                sx={{\r\n                    color: \"text.secondary\",\r\n                    minWidth: \"40px\",\r\n                    fontSize: \"3vh\",\r\n                }}\r\n            >\r\n                {formatFloatToOneDecimal(starCount)}\r\n            </Typography>\r\n        </Stack>\r\n    );\r\n}\r\n\r\nconst RatingDetailsReview = ({ product }) => {\r\n    const { totalRating, Ratings, rateCount } = product;\r\n    return (\r\n        <Box>\r\n            <Grid container>\r\n                <GridStyle item xs={6} md={6} lg={6}>\r\n                    <Typography\r\n                        sx={{\r\n                            color: \"error.main\",\r\n                            fontSize: \"27vh\",\r\n                            fontWeight: \"bold\",\r\n                            animation: `${pulsateAnimation} 4s infinite`,\r\n                        }}\r\n                    >\r\n                        {formatFloatToOneDecimal(totalRating)}/5\r\n                    </Typography>\r\n                    <RatingStyle readOnly value={totalRating} precision={0.1} />\r\n                    <Typography\r\n                        variant=\"body1\"\r\n                        sx={{\r\n                            color: \"text.secondary\",\r\n                            fontSize: \"4vh\",\r\n                        }}\r\n                    >\r\n                        ({rateCount}&nbsp;reviews)\r\n                    </Typography>\r\n                </GridStyle>\r\n\r\n                <GridStyle item xs={6} md={6} lg={6} marginTop=\"7vh\">\r\n                    <Stack spacing=\"6vh\" sx={{ width: \"90%\" }}>\r\n                        {Ratings.map((rating) => (\r\n                            <ProgressItem key={rating.name} star={rating} />\r\n                        ))}\r\n                    </Stack>\r\n                </GridStyle>\r\n            </Grid>\r\n        </Box>\r\n    );\r\n};\r\n\r\nexport default RatingDetailsReview;\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Link\\PhoneLinkDialog.js", ["709", "710", "711", "712", "713"], [], "import { useState, useEffect } from \"react\";\r\nimport {\r\n  TextField,\r\n  Button,\r\n  Dialog,\r\n  DialogTitle,\r\n  DialogContent,\r\n  DialogActions,\r\n  Box,\r\n  Typography,\r\n  Grid,\r\n  FormControl,\r\n  InputLabel,\r\n  Select,\r\n  MenuItem,\r\n} from \"@mui/material\";\r\n\r\nimport { CreateContact, EditContact } from \"../../../ContactData.ts\";\r\n\r\nimport { toast } from \"react-toastify\";\r\nimport Chip from \"@mui/material/Chip\";\r\n\r\n// Country codes data (same as WhatsApp)\r\nconst countryCodes = [\r\n  {\r\n    code: \"+1\",\r\n    country: \"United States\",\r\n    flag: \"https://flagcdn.com/w20/us.png\",\r\n  },\r\n  { code: \"+1\", country: \"Canada\", flag: \"https://flagcdn.com/w20/ca.png\" },\r\n  {\r\n    code: \"+44\",\r\n    country: \"United Kingdom\",\r\n    flag: \"https://flagcdn.com/w20/gb.png\",\r\n  },\r\n  { code: \"+49\", country: \"Germany\", flag: \"https://flagcdn.com/w20/de.png\" },\r\n  { code: \"+33\", country: \"France\", flag: \"https://flagcdn.com/w20/fr.png\" },\r\n  { code: \"+39\", country: \"Italy\", flag: \"https://flagcdn.com/w20/it.png\" },\r\n  { code: \"+34\", country: \"Spain\", flag: \"https://flagcdn.com/w20/es.png\" },\r\n  {\r\n    code: \"+31\",\r\n    country: \"Netherlands\",\r\n    flag: \"https://flagcdn.com/w20/nl.png\",\r\n  },\r\n  {\r\n    code: \"+41\",\r\n    country: \"Switzerland\",\r\n    flag: \"https://flagcdn.com/w20/ch.png\",\r\n  },\r\n  { code: \"+43\", country: \"Austria\", flag: \"https://flagcdn.com/w20/at.png\" },\r\n  { code: \"+32\", country: \"Belgium\", flag: \"https://flagcdn.com/w20/be.png\" },\r\n  { code: \"+45\", country: \"Denmark\", flag: \"https://flagcdn.com/w20/dk.png\" },\r\n  { code: \"+46\", country: \"Sweden\", flag: \"https://flagcdn.com/w20/se.png\" },\r\n  { code: \"+47\", country: \"Norway\", flag: \"https://flagcdn.com/w20/no.png\" },\r\n  { code: \"+358\", country: \"Finland\", flag: \"https://flagcdn.com/w20/fi.png\" },\r\n  { code: \"+91\", country: \"India\", flag: \"https://flagcdn.com/w20/in.png\" },\r\n  { code: \"+86\", country: \"China\", flag: \"https://flagcdn.com/w20/cn.png\" },\r\n  { code: \"+81\", country: \"Japan\", flag: \"https://flagcdn.com/w20/jp.png\" },\r\n  {\r\n    code: \"+82\",\r\n    country: \"South Korea\",\r\n    flag: \"https://flagcdn.com/w20/kr.png\",\r\n  },\r\n  { code: \"+65\", country: \"Singapore\", flag: \"https://flagcdn.com/w20/sg.png\" },\r\n  { code: \"+60\", country: \"Malaysia\", flag: \"https://flagcdn.com/w20/my.png\" },\r\n  { code: \"+66\", country: \"Thailand\", flag: \"https://flagcdn.com/w20/th.png\" },\r\n  { code: \"+84\", country: \"Vietnam\", flag: \"https://flagcdn.com/w20/vn.png\" },\r\n  {\r\n    code: \"+63\",\r\n    country: \"Philippines\",\r\n    flag: \"https://flagcdn.com/w20/ph.png\",\r\n  },\r\n  { code: \"+62\", country: \"Indonesia\", flag: \"https://flagcdn.com/w20/id.png\" },\r\n  { code: \"+61\", country: \"Australia\", flag: \"https://flagcdn.com/w20/au.png\" },\r\n  {\r\n    code: \"+64\",\r\n    country: \"New Zealand\",\r\n    flag: \"https://flagcdn.com/w20/nz.png\",\r\n  },\r\n  {\r\n    code: \"+27\",\r\n    country: \"South Africa\",\r\n    flag: \"https://flagcdn.com/w20/za.png\",\r\n  },\r\n  { code: \"+20\", country: \"Egypt\", flag: \"https://flagcdn.com/w20/eg.png\" },\r\n  { code: \"+216\", country: \"Tunisia\", flag: \"https://flagcdn.com/w20/tn.png\" },\r\n  { code: \"+234\", country: \"Nigeria\", flag: \"https://flagcdn.com/w20/ng.png\" },\r\n  { code: \"+254\", country: \"Kenya\", flag: \"https://flagcdn.com/w20/ke.png\" },\r\n  { code: \"+971\", country: \"UAE\", flag: \"https://flagcdn.com/w20/ae.png\" },\r\n  {\r\n    code: \"+966\",\r\n    country: \"Saudi Arabia\",\r\n    flag: \"https://flagcdn.com/w20/sa.png\",\r\n  },\r\n  { code: \"+974\", country: \"Qatar\", flag: \"https://flagcdn.com/w20/qa.png\" },\r\n  { code: \"+965\", country: \"Kuwait\", flag: \"https://flagcdn.com/w20/kw.png\" },\r\n  { code: \"+973\", country: \"Bahrain\", flag: \"https://flagcdn.com/w20/bh.png\" },\r\n  { code: \"+968\", country: \"Oman\", flag: \"https://flagcdn.com/w20/om.png\" },\r\n  { code: \"+972\", country: \"Israel\", flag: \"https://flagcdn.com/w20/il.png\" },\r\n  { code: \"+90\", country: \"Turkey\", flag: \"https://flagcdn.com/w20/tr.png\" },\r\n  { code: \"+7\", country: \"Russia\", flag: \"https://flagcdn.com/w20/ru.png\" },\r\n  { code: \"+380\", country: \"Ukraine\", flag: \"https://flagcdn.com/w20/ua.png\" },\r\n  { code: \"+48\", country: \"Poland\", flag: \"https://flagcdn.com/w20/pl.png\" },\r\n  {\r\n    code: \"+420\",\r\n    country: \"Czech Republic\",\r\n    flag: \"https://flagcdn.com/w20/cz.png\",\r\n  },\r\n  { code: \"+36\", country: \"Hungary\", flag: \"https://flagcdn.com/w20/hu.png\" },\r\n  { code: \"+40\", country: \"Romania\", flag: \"https://flagcdn.com/w20/ro.png\" },\r\n  { code: \"+359\", country: \"Bulgaria\", flag: \"https://flagcdn.com/w20/bg.png\" },\r\n  { code: \"+385\", country: \"Croatia\", flag: \"https://flagcdn.com/w20/hr.png\" },\r\n  { code: \"+381\", country: \"Serbia\", flag: \"https://flagcdn.com/w20/rs.png\" },\r\n  { code: \"+55\", country: \"Brazil\", flag: \"https://flagcdn.com/w20/br.png\" },\r\n  { code: \"+52\", country: \"Mexico\", flag: \"https://flagcdn.com/w20/mx.png\" },\r\n  { code: \"+54\", country: \"Argentina\", flag: \"https://flagcdn.com/w20/ar.png\" },\r\n  { code: \"+56\", country: \"Chile\", flag: \"https://flagcdn.com/w20/cl.png\" },\r\n  { code: \"+57\", country: \"Colombia\", flag: \"https://flagcdn.com/w20/co.png\" },\r\n  { code: \"+51\", country: \"Peru\", flag: \"https://flagcdn.com/w20/pe.png\" },\r\n  { code: \"+58\", country: \"Venezuela\", flag: \"https://flagcdn.com/w20/ve.png\" },\r\n  { code: \"+593\", country: \"Ecuador\", flag: \"https://flagcdn.com/w20/ec.png\" },\r\n  { code: \"+595\", country: \"Paraguay\", flag: \"https://flagcdn.com/w20/py.png\" },\r\n  { code: \"+598\", country: \"Uruguay\", flag: \"https://flagcdn.com/w20/uy.png\" },\r\n];\r\n\r\nconst PhoneLinkDialog = ({\r\n  setOpenPhoneDialog,\r\n  openPhoneDialog,\r\n  Id,\r\n  editingContact = null,\r\n  fetchProfile,\r\n  clearEditingContact,\r\n}) => {\r\n  const [phoneNumber, setPhoneNumber] = useState(\"\");\r\n  const [contactName, setContactName] = useState(\"\");\r\n  const [selectedCountryCode, setSelectedCountryCode] = useState(\"+1\");\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  // Function to extract country code from a phone number\r\n  const extractCountryCode = (phoneNumber) => {\r\n    if (!phoneNumber || !phoneNumber.startsWith(\"+\")) {\r\n      return \"+1\"; // Default to US\r\n    }\r\n\r\n    // Sort country codes by length (longest first) to match correctly\r\n    const sortedCodes = countryCodes\r\n      .map((c) => c.code)\r\n      .filter((code, index, arr) => arr.indexOf(code) === index) // Remove duplicates\r\n      .sort((a, b) => b.length - a.length);\r\n\r\n    for (const code of sortedCodes) {\r\n      if (phoneNumber.startsWith(code)) {\r\n        return code;\r\n      }\r\n    }\r\n\r\n    return \"+1\"; // Default fallback\r\n  };\r\n\r\n  // Function to extract the number without country code\r\n  const extractNumberWithoutCode = (phoneNumber, countryCode) => {\r\n    if (!phoneNumber || !phoneNumber.startsWith(countryCode)) {\r\n      return \"\";\r\n    }\r\n    return phoneNumber.substring(countryCode.length).replace(/^\\s+/, \"\");\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (editingContact) {\r\n      const fullNumber = editingContact.LinkUrl || \"\";\r\n      const extractedCode = extractCountryCode(fullNumber);\r\n      const numberWithoutCode = extractNumberWithoutCode(\r\n        fullNumber,\r\n        extractedCode\r\n      );\r\n      setSelectedCountryCode(extractedCode);\r\n      setPhoneNumber(numberWithoutCode);\r\n      setContactName(editingContact.Title || \"\");\r\n    } else {\r\n      setSelectedCountryCode(\"+1\");\r\n      setPhoneNumber(\"\");\r\n      setContactName(\"\");\r\n    }\r\n  }, [editingContact, openPhoneDialog]);\r\n\r\n  const handleContactNameChange = (event) => {\r\n    setContactName(event.target.value);\r\n  };\r\n\r\n  const validatePhoneNumber = (countryCode, phoneNumber) => {\r\n    if (!phoneNumber || phoneNumber.trim() === \"\") {\r\n      return { isValid: false, error: \"Phone number is required\" };\r\n    }\r\n\r\n    if (!countryCode) {\r\n      return { isValid: false, error: \"Country code is required\" };\r\n    }\r\n\r\n    // Remove all spaces, dashes, parentheses, and other formatting from phone number\r\n    const cleanNumber = phoneNumber.replace(/[\\s\\-\\(\\)\\.]/g, \"\");\r\n\r\n    // Check if it contains only digits after cleaning\r\n    if (!/^\\d+$/.test(cleanNumber)) {\r\n      return {\r\n        isValid: false,\r\n        error:\r\n          \"Phone number should contain only digits, spaces, dashes, or parentheses\",\r\n      };\r\n    }\r\n\r\n    // Country-specific validation patterns (same as WhatsApp)\r\n    const countryValidation = {\r\n      \"+1\": {\r\n        // US/Canada\r\n        minLength: 10,\r\n        maxLength: 10,\r\n        pattern: /^[2-9]\\d{9}$/,\r\n        errorMsg: \"US/Canada numbers should be 10 digits starting with 2-9\",\r\n      },\r\n      \"+44\": {\r\n        // UK\r\n        minLength: 10,\r\n        maxLength: 11,\r\n        pattern: /^[1-9]\\d{9,10}$/,\r\n        errorMsg: \"UK numbers should be 10-11 digits\",\r\n      },\r\n      \"+49\": {\r\n        // Germany\r\n        minLength: 10,\r\n        maxLength: 12,\r\n        pattern: /^[1-9]\\d{9,11}$/,\r\n        errorMsg: \"German numbers should be 10-12 digits\",\r\n      },\r\n      \"+33\": {\r\n        // France\r\n        minLength: 9,\r\n        maxLength: 9,\r\n        pattern: /^[1-9]\\d{8}$/,\r\n        errorMsg: \"French numbers should be 9 digits starting with 1-9\",\r\n      },\r\n      \"+39\": {\r\n        // Italy\r\n        minLength: 9,\r\n        maxLength: 11,\r\n        pattern: /^[0-9]\\d{8,10}$/,\r\n        errorMsg: \"Italian numbers should be 9-11 digits\",\r\n      },\r\n      \"+34\": {\r\n        // Spain\r\n        minLength: 9,\r\n        maxLength: 9,\r\n        pattern: /^[6-9]\\d{8}$/,\r\n        errorMsg: \"Spanish mobile numbers should be 9 digits starting with 6-9\",\r\n      },\r\n      \"+91\": {\r\n        // India\r\n        minLength: 10,\r\n        maxLength: 10,\r\n        pattern: /^[6-9]\\d{9}$/,\r\n        errorMsg: \"Indian mobile numbers should be 10 digits starting with 6-9\",\r\n      },\r\n      \"+86\": {\r\n        // China\r\n        minLength: 11,\r\n        maxLength: 11,\r\n        pattern: /^1[3-9]\\d{9}$/,\r\n        errorMsg:\r\n          \"Chinese mobile numbers should be 11 digits starting with 13-19\",\r\n      },\r\n      \"+81\": {\r\n        // Japan\r\n        minLength: 10,\r\n        maxLength: 11,\r\n        pattern: /^[7-9]\\d{9,10}$/,\r\n        errorMsg:\r\n          \"Japanese mobile numbers should be 10-11 digits starting with 7-9\",\r\n      },\r\n      \"+82\": {\r\n        // South Korea\r\n        minLength: 9,\r\n        maxLength: 10,\r\n        pattern: /^1[0-9]\\d{7,8}$/,\r\n        errorMsg:\r\n          \"Korean mobile numbers should be 9-10 digits starting with 10-19\",\r\n      },\r\n      \"+971\": {\r\n        // UAE\r\n        minLength: 9,\r\n        maxLength: 9,\r\n        pattern: /^5\\d{8}$/,\r\n        errorMsg: \"UAE mobile numbers should be 9 digits starting with 5\",\r\n      },\r\n      \"+966\": {\r\n        // Saudi Arabia\r\n        minLength: 9,\r\n        maxLength: 9,\r\n        pattern: /^5\\d{8}$/,\r\n        errorMsg: \"Saudi mobile numbers should be 9 digits starting with 5\",\r\n      },\r\n      \"+55\": {\r\n        // Brazil\r\n        minLength: 10,\r\n        maxLength: 11,\r\n        pattern: /^[1-9]\\d{9,10}$/,\r\n        errorMsg: \"Brazilian mobile numbers should be 10-11 digits\",\r\n      },\r\n      \"+52\": {\r\n        // Mexico\r\n        minLength: 10,\r\n        maxLength: 10,\r\n        pattern: /^[1-9]\\d{9}$/,\r\n        errorMsg: \"Mexican mobile numbers should be 10 digits\",\r\n      },\r\n      \"+216\": {\r\n        // Tunisia\r\n        minLength: 8,\r\n        maxLength: 8,\r\n        pattern: /^[0-9]{8}$/,\r\n        errorMsg: \"Tunisian mobile numbers should be exactly 8 digits\",\r\n      },\r\n    };\r\n\r\n    const validation = countryValidation[countryCode];\r\n\r\n    if (validation) {\r\n      // Check length\r\n      if (cleanNumber.length < validation.minLength) {\r\n        return {\r\n          isValid: false,\r\n          error: `Number too short. ${validation.errorMsg}`,\r\n        };\r\n      }\r\n      if (cleanNumber.length > validation.maxLength) {\r\n        return {\r\n          isValid: false,\r\n          error: `Number too long. ${validation.errorMsg}`,\r\n        };\r\n      }\r\n\r\n      // Check pattern\r\n      if (!validation.pattern.test(cleanNumber)) {\r\n        return { isValid: false, error: validation.errorMsg };\r\n      }\r\n    } else {\r\n      // General validation for countries not specifically listed\r\n      if (cleanNumber.length < 7) {\r\n        return {\r\n          isValid: false,\r\n          error: \"Phone number too short (minimum 7 digits)\",\r\n        };\r\n      }\r\n      if (cleanNumber.length > 15) {\r\n        return {\r\n          isValid: false,\r\n          error: \"Phone number too long (maximum 15 digits)\",\r\n        };\r\n      }\r\n    }\r\n\r\n    return { isValid: true, error: null };\r\n  };\r\n\r\n  const isValidPhoneNumber = (countryCode, phoneNumber) => {\r\n    const validation = validatePhoneNumber(countryCode, phoneNumber);\r\n    return validation.isValid;\r\n  };\r\n\r\n  const getValidationError = (countryCode, phoneNumber) => {\r\n    const validation = validatePhoneNumber(countryCode, phoneNumber);\r\n    return validation.error;\r\n  };\r\n\r\n  const handleDone = async () => {\r\n    // Validate using separated country code and phone number\r\n    const validationError = getValidationError(\r\n      selectedCountryCode,\r\n      phoneNumber\r\n    );\r\n    if (validationError) {\r\n      toast.error(validationError, {\r\n        position: \"top-center\",\r\n        autoClose: 3000,\r\n      });\r\n      return;\r\n    }\r\n\r\n    // Combine country code with number for storage\r\n    const fullNumber = selectedCountryCode + phoneNumber.replace(/^\\s+/, \"\");\r\n\r\n    if (!contactName.trim()) {\r\n      toast.error(\"Contact name is required\", {\r\n        position: \"top-center\",\r\n        autoClose: 2000,\r\n      });\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    let response;\r\n\r\n    try {\r\n      if (editingContact) {\r\n        response = await EditContact({\r\n          Id: editingContact.Id,\r\n          ContactInfo: fullNumber,\r\n          Category: \"PhoneNumber\",\r\n          Title: contactName.trim(),\r\n          isPublic: true,\r\n        });\r\n      } else {\r\n        response = await CreateContact({\r\n          UserId: Id,\r\n          ContactInfo: fullNumber,\r\n          Category: \"PhoneNumber\",\r\n          Title: contactName.trim(),\r\n          isPublic: true,\r\n        });\r\n      }\r\n\r\n      localStorage.setItem(\"isLinksCardVisible\", \"true\");\r\n\r\n      setContactName(\"\");\r\n      setPhoneNumber(\"\");\r\n      setContactName(\"\");\r\n      if (clearEditingContact) clearEditingContact();\r\n      setOpenPhoneDialog(false);\r\n\r\n      if (response) {\r\n        toast.success(\r\n          editingContact ? \"Phone contact updated\" : \"Phone contact added\",\r\n          {\r\n            position: \"top-center\",\r\n            autoClose: 1000,\r\n          }\r\n        );\r\n        if (fetchProfile) fetchProfile();\r\n      } else {\r\n        toast.error(\"Error while saving phone contact\", {\r\n          position: \"top-center\",\r\n          autoClose: 1000,\r\n        });\r\n      }\r\n    } catch (error) {\r\n      toast.error(\"Error while saving phone contact\", {\r\n        position: \"top-center\",\r\n        autoClose: 1000,\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog\r\n      open={openPhoneDialog}\r\n      onClose={() => {\r\n        setPhoneNumber(\"\");\r\n        setContactName(\"\");\r\n        if (clearEditingContact) clearEditingContact();\r\n        setOpenPhoneDialog(false);\r\n      }}\r\n    >\r\n      <DialogTitle>\r\n        {editingContact ? \"Edit Phone Contact\" : \"Add Phone Contact\"}\r\n      </DialogTitle>\r\n      <DialogContent>\r\n        <TextField\r\n          name=\"contactName\"\r\n          autoFocus\r\n          margin=\"dense\"\r\n          label=\"Contact Name\"\r\n          type=\"text\"\r\n          fullWidth\r\n          required\r\n          value={contactName}\r\n          onChange={handleContactNameChange}\r\n          helperText={contactName === \"\" ? \"Contact name is required\" : \"\"}\r\n          sx={{ mb: 2 }}\r\n        />\r\n        <Grid container spacing={2}>\r\n          <Grid item xs={12} sm={4}>\r\n            <FormControl fullWidth margin=\"dense\">\r\n              <InputLabel>Country</InputLabel>\r\n              <Select\r\n                value={selectedCountryCode}\r\n                onChange={(e) => setSelectedCountryCode(e.target.value)}\r\n                label=\"Country\"\r\n                sx={{\r\n                  \"& .MuiSelect-select\": {\r\n                    padding: { xs: \"12px 14px\", sm: \"16.5px 14px\" },\r\n                    fontSize: { xs: \"0.875rem\", sm: \"1rem\" },\r\n                  },\r\n                }}\r\n                renderValue={(value) => {\r\n                  const country = countryCodes.find((c) => c.code === value);\r\n                  return country ? (\r\n                    <Box\r\n                      sx={{\r\n                        display: \"flex\",\r\n                        alignItems: \"center\",\r\n                        gap: { xs: 0.5, sm: 1 },\r\n                        minWidth: 0,\r\n                      }}\r\n                    >\r\n                      <img\r\n                        src={country.flag}\r\n                        alt={country.country}\r\n                        style={{\r\n                          width: 20,\r\n                          height: 15,\r\n                          flexShrink: 0,\r\n                        }}\r\n                      />\r\n                      <span\r\n                        style={{\r\n                          fontSize: \"inherit\",\r\n                          overflow: \"hidden\",\r\n                          textOverflow: \"ellipsis\",\r\n                          whiteSpace: \"nowrap\",\r\n                        }}\r\n                      >\r\n                        {value}\r\n                      </span>\r\n                    </Box>\r\n                  ) : (\r\n                    value\r\n                  );\r\n                }}\r\n              >\r\n                {countryCodes.map((country, index) => (\r\n                  <MenuItem\r\n                    key={`${country.code}-${index}`}\r\n                    value={country.code}\r\n                    sx={{\r\n                      padding: { xs: \"8px 16px\", sm: \"6px 16px\" },\r\n                      minHeight: { xs: \"48px\", sm: \"auto\" },\r\n                    }}\r\n                  >\r\n                    <Box\r\n                      sx={{\r\n                        display: \"flex\",\r\n                        alignItems: \"center\",\r\n                        gap: 1,\r\n                        width: \"100%\",\r\n                        minWidth: 0,\r\n                      }}\r\n                    >\r\n                      <img\r\n                        src={country.flag}\r\n                        alt={country.country}\r\n                        style={{\r\n                          width: 20,\r\n                          height: 15,\r\n                          flexShrink: 0,\r\n                        }}\r\n                      />\r\n                      <span\r\n                        style={{\r\n                          fontWeight: 500,\r\n                          flexShrink: 0,\r\n                        }}\r\n                      >\r\n                        {country.code}\r\n                      </span>\r\n                      <span\r\n                        style={{\r\n                          fontSize: \"0.875rem\",\r\n                          color: \"#666\",\r\n                          overflow: \"hidden\",\r\n                          textOverflow: \"ellipsis\",\r\n                          whiteSpace: \"nowrap\",\r\n                          flex: 1,\r\n                          minWidth: 0,\r\n                        }}\r\n                      >\r\n                        {country.country}\r\n                      </span>\r\n                    </Box>\r\n                  </MenuItem>\r\n                ))}\r\n              </Select>\r\n            </FormControl>\r\n          </Grid>\r\n          <Grid item xs={12} sm={8}>\r\n            <TextField\r\n              name=\"PhoneNumber\"\r\n              margin=\"dense\"\r\n              label=\"Phone Number\"\r\n              type=\"tel\"\r\n              fullWidth\r\n              required\r\n              value={phoneNumber}\r\n              onChange={(e) =>\r\n                setPhoneNumber(e.target.value.replace(/[^\\d\\s\\-\\(\\)]/g, \"\"))\r\n              }\r\n              error={\r\n                phoneNumber !== \"\" &&\r\n                !isValidPhoneNumber(selectedCountryCode, phoneNumber)\r\n              }\r\n              helperText={\r\n                phoneNumber === \"\"\r\n                  ? \"Phone number is required\"\r\n                  : phoneNumber !== \"\" &&\r\n                    !isValidPhoneNumber(selectedCountryCode, phoneNumber)\r\n                  ? getValidationError(selectedCountryCode, phoneNumber)\r\n                  : \"✓ Valid phone number\"\r\n              }\r\n              placeholder=\"************\"\r\n              inputProps={{\r\n                maxLength: 15,\r\n              }}\r\n              sx={{\r\n                \"& .MuiInputBase-input\": {\r\n                  fontSize: { xs: \"16px\", sm: \"1rem\" }, // Prevents zoom on iOS\r\n                  padding: { xs: \"12px 14px\", sm: \"16.5px 14px\" },\r\n                },\r\n                \"& .MuiFormHelperText-root\": {\r\n                  fontSize: { xs: \"0.75rem\", sm: \"0.75rem\" },\r\n                  marginTop: { xs: \"4px\", sm: \"3px\" },\r\n                },\r\n              }}\r\n            />\r\n          </Grid>\r\n        </Grid>\r\n\r\n        {/* Preview of full number */}\r\n        {phoneNumber && (\r\n          <Box\r\n            sx={{\r\n              mt: { xs: 2, sm: 1 },\r\n              mb: { xs: 2, sm: 1 },\r\n              display: \"flex\",\r\n              justifyContent: { xs: \"center\", sm: \"flex-start\" },\r\n            }}\r\n          >\r\n            <Chip\r\n              label={`Full Number: ${selectedCountryCode} ${phoneNumber}`}\r\n              variant=\"outlined\"\r\n              size=\"small\"\r\n              color=\"primary\"\r\n              sx={{\r\n                fontSize: { xs: \"0.75rem\", sm: \"0.8125rem\" },\r\n                height: { xs: \"28px\", sm: \"24px\" },\r\n                \"& .MuiChip-label\": {\r\n                  padding: { xs: \"0 8px\", sm: \"0 12px\" },\r\n                },\r\n              }}\r\n            />\r\n          </Box>\r\n        )}\r\n        {/* Hints and Tips Section */}\r\n        <Box\r\n          mt={2}\r\n          p={2}\r\n          sx={{\r\n            backgroundColor: \"#f0f0f0\",\r\n            borderRadius: \"5px\",\r\n          }}\r\n        >\r\n          <Typography variant=\"subtitle1\" color=\"textPrimary\">\r\n            Tips for Adding Phone Contact\r\n          </Typography>\r\n          <Typography variant=\"body2\" color=\"textSecondary\">\r\n            - Give your contact a descriptive name (e.g., \"Work Phone\",\r\n            \"Personal Phone\")\r\n          </Typography>\r\n          <Typography variant=\"body2\" color=\"textSecondary\">\r\n            - Enter the phone number without spaces, dashes, or symbols\r\n          </Typography>\r\n          <Typography variant=\"body2\" color=\"textSecondary\">\r\n            - Include the country code if needed (e.g., +1 for the US)\r\n          </Typography>\r\n          <Typography variant=\"body2\" color=\"textSecondary\">\r\n            - You can add multiple phone contacts with different names\r\n          </Typography>\r\n        </Box>\r\n      </DialogContent>\r\n      <DialogActions>\r\n        <Button\r\n          onClick={() => {\r\n            setContactName(\"\");\r\n            setPhoneNumber(\"\");\r\n            setContactName(\"\");\r\n            if (clearEditingContact) clearEditingContact();\r\n            setOpenPhoneDialog(false);\r\n          }}\r\n        >\r\n          Cancel\r\n        </Button>\r\n        <Button\r\n          onClick={handleDone}\r\n          disabled={\r\n            phoneNumber === \"\" ||\r\n            !isValidPhoneNumber(selectedCountryCode, phoneNumber) ||\r\n            contactName.trim() === \"\" ||\r\n            isLoading\r\n          }\r\n        >\r\n          {isLoading ? \"Saving...\" : editingContact ? \"Update\" : \"Add\"}\r\n        </Button>\r\n      </DialogActions>\r\n    </Dialog>\r\n  );\r\n};\r\n\r\nexport default PhoneLinkDialog;\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Rating\\VerticalLinearStepper.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\auth\\signup\\PhotoSelector.js", ["714"], [], "import { useRef } from \"react\";\r\nimport { <PERSON><PERSON>, CircularProgress } from \"@mui/material\";\r\nimport PostAddIcon from \"@mui/icons-material/PostAdd\";\r\nimport { toast } from \"react-toastify\";\r\n\r\nconst PhotoSelector = ({ onSelect }) => {\r\n  const fileInputRef = useRef(null);\r\n  const maxSize = 1024 * 1024;\r\n\r\n  const handleFileChange = (event) => {\r\n    const file = event.target.files[0];\r\n\r\n    if (file) {\r\n      // Validate file type\r\n      if (!file.type.startsWith(\"image/\")) {\r\n        toast.error(\"Please select a valid image file\", {\r\n          position: \"top-center\",\r\n          autoClose: 3000,\r\n        });\r\n        return;\r\n      }\r\n\r\n      // Validate file size\r\n      if (file.size > maxSize) {\r\n        toast.error(\r\n          \"File size exceeds the 1MB limit. Please select a smaller file.\",\r\n          {\r\n            position: \"top-center\",\r\n            autoClose: 3000,\r\n          }\r\n        );\r\n        return;\r\n      }\r\n\r\n      const reader = new FileReader();\r\n      reader.onload = () => {\r\n        try {\r\n          onSelect(reader.result);\r\n          toast.success(\"Image uploaded successfully\", {\r\n            position: \"top-center\",\r\n            autoClose: 1000,\r\n          });\r\n        } catch (error) {\r\n          toast.error(\"Error processing image\", {\r\n            position: \"top-center\",\r\n            autoClose: 3000,\r\n          });\r\n        }\r\n      };\r\n      reader.onerror = () => {\r\n        toast.error(\"Error reading file\", {\r\n          position: \"top-center\",\r\n          autoClose: 3000,\r\n        });\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  const handleButtonClick = () => {\r\n    fileInputRef.current.click();\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <input\r\n        type=\"file\"\r\n        accept=\"image/*\"\r\n        ref={fileInputRef}\r\n        style={{ display: \"none\" }}\r\n        onChange={handleFileChange}\r\n      />\r\n      <Button\r\n        variant=\"contained\"\r\n        onClick={handleButtonClick}\r\n        style={{\r\n          position: \"absolute\",\r\n          width: \"0px\",\r\n          height: \"0px\",\r\n          border: \"none\",\r\n          padding: \"0\",\r\n          fontSize: \"20px\",\r\n          backgroundColor: \"transparent\",\r\n          color: \"#ff715b\",\r\n        }}\r\n      >\r\n        <i className=\"bi bi-plus-circle-fill\"></i>\r\n      </Button>\r\n    </>\r\n  );\r\n};\r\n\r\nexport const FileSelector = ({ onSelect, isLoading = false }) => {\r\n  const fileInputRef = useRef(null);\r\n  const maxSize = 1024 * 1024;\r\n\r\n  const handleFileChange = (event) => {\r\n    const file = event.target.files[0];\r\n\r\n    if (file) {\r\n      if (file.type != \"application/pdf\") {\r\n        alert(\"Invalid file type. Only PDF are allowed.\");\r\n        return;\r\n      }\r\n\r\n      if (file.size > maxSize) {\r\n        alert(\"File size exceeds the 1MB limit. Please select a smaller file.\");\r\n        return;\r\n      }\r\n\r\n      const reader = new FileReader();\r\n      reader.onload = () => {\r\n        onSelect(reader.result);\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  const handleButtonClick = () => {\r\n    fileInputRef.current.click();\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <input\r\n        type=\"file\"\r\n        accept=\"application/pdf\"\r\n        ref={fileInputRef}\r\n        style={{ display: \"none\" }}\r\n        onChange={handleFileChange}\r\n      />\r\n      <Button\r\n        variant=\"contained\"\r\n        onClick={handleButtonClick}\r\n        color=\"primary\"\r\n        disabled={isLoading}\r\n      >\r\n        <span\r\n          style={{\r\n            marginRight: \"10px\",\r\n          }}\r\n        >\r\n          {isLoading ? \"Uploading...\" : \"Upload\"}\r\n        </span>\r\n        {isLoading ? (\r\n          <CircularProgress size={20} color=\"inherit\" />\r\n        ) : (\r\n          <PostAddIcon />\r\n        )}\r\n      </Button>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default PhotoSelector;\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Rating\\RatingDetailsReview.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\auth\\signup\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\layouts\\dashboard\\DashboardLayout.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\utils\\cssStyles.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\auth\\login\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\overrides\\Table.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\overrides\\Button.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\overrides\\Card.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\overrides\\Input.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\overrides\\Tooltip.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\overrides\\Backdrop.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\overrides\\Paper.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\overrides\\Autocomplete.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\theme\\overrides\\Typography.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\auth\\signup\\SquarePhotoSelector.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Rating\\ThankYouCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\ContactData.ts", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\utils\\formatNumber.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\iconify\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Contact\\AddCvDialog.js", ["715", "716", "717", "718"], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\auth\\signup\\SignUpForm.js", ["719"], [], "// SignUpForm.js\r\nimport { useState, useEffect } from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { Register } from \"../../../AuthenticationData.ts\";\r\nimport { ToastContainer, toast } from \"react-toastify\";\r\nimport \"react-toastify/dist/ReactToastify.css\";\r\nimport Register1 from \"./Register1\";\r\nimport Register2 from \"./Register2\";\r\nimport Register3 from \"./Register3\";\r\nimport RegisterConfirm from \"./RegisterConfirm\";\r\n\r\nexport default function SignUpForm() {\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [formData, setFormData] = useState({\r\n    email: \"\",\r\n    firstName: \"\",\r\n    lastName: \"\",\r\n    userName: \"\",\r\n    password: \"\",\r\n    gender: \"\",\r\n    contactInfo: \"\",\r\n    categoryUser: \"\",\r\n    contactCategory: \"PhoneNumber\",\r\n    profilePicture: \"\",\r\n  });\r\n  const [registrationSuccess, setRegistrationSuccess] = useState(false);\r\n  const [isFormValid, setFormValid] = useState(false); // Validation state\r\n\r\n  const navigate = useNavigate();\r\n\r\n  const onSubmit = async () => {\r\n    try {\r\n      const response = await Register(formData);\r\n\r\n      if (response.error) {\r\n        throw new Error(response.error);\r\n      }\r\n\r\n      navigate(\"/VerifyMail\");\r\n      setRegistrationSuccess(true);\r\n    } catch (error) {\r\n      setCurrentPage(3);\r\n      setFormData((prevData) => ({ ...prevData, password: \"\" }));\r\n      toast.error(error.message, {\r\n        position: \"top-center\",\r\n        autoClose: 1000,\r\n      });\r\n    }\r\n  };\r\n  const handleNext = (data) => {\r\n    setFormData((prevData) => ({ ...prevData, ...data }));\r\n    setCurrentPage((prevPage) => prevPage + 1);\r\n  };\r\n\r\n  const handlePrevious = (data) => {\r\n    setFormData((prevData) => ({ ...prevData, ...data }));\r\n    setCurrentPage((prevPage) => prevPage - 1);\r\n  };\r\n\r\n  useEffect(() => {\r\n    validateForm();\r\n\r\n    if (registrationSuccess) {\r\n      toast.success(\"Registration successful!\", {\r\n        position: \"top-right\",\r\n        autoClose: 3000,\r\n        hideProgressBar: false,\r\n        closeOnClick: true,\r\n        pauseOnHover: true,\r\n      });\r\n    }\r\n  }, [registrationSuccess, formData]);\r\n\r\n  const validateForm = () => {\r\n    const { firstName, lastName, userName, categoryUser } = formData;\r\n\r\n    const isStep1Valid =\r\n      firstName.trim() !== \"\" &&\r\n      lastName.trim() !== \"\" &&\r\n      userName.trim() !== \"\" &&\r\n      categoryUser.trim() !== \"\";\r\n    const isStep2Valid = formData.contactInfo.trim().length === 8;\r\n\r\n    setFormValid(isStep1Valid && isStep2Valid /* && ... */);\r\n  };\r\n\r\n  const renderPage = () => {\r\n    switch (currentPage) {\r\n      case 1:\r\n        return (\r\n          <Register1\r\n            onNext={handleNext}\r\n            data={formData}\r\n            isFormValid={isFormValid}\r\n          />\r\n        );\r\n      case 2:\r\n        return (\r\n          <Register2\r\n            onNext={handleNext}\r\n            onPrevious={handlePrevious}\r\n            data={formData}\r\n            isFormValid={isFormValid}\r\n          />\r\n        );\r\n      case 3:\r\n        return (\r\n          <Register3\r\n            onNext={handleNext}\r\n            onPrevious={handlePrevious}\r\n            data={formData}\r\n            isFormValid={isFormValid}\r\n          />\r\n        );\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <div>\r\n        {currentPage > 3 ? (\r\n          <RegisterConfirm onSubmit={onSubmit} />\r\n        ) : (\r\n          renderPage()\r\n        )}\r\n      </div>\r\n      <ToastContainer />\r\n    </>\r\n  );\r\n}\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppNewsUpdate.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\auth\\signup\\Register2.js", ["720"], [], "import React, { useState, useEffect } from \"react\";\r\nimport { <PERSON>ack, TextField, Typography } from \"@mui/material\";\r\nimport { LoadingButton } from \"@mui/lab\";\r\nimport { ToastContainer, toast } from \"react-toastify\";\r\n\r\nconst Register2 = ({ onNext, onPrevious, data }) => {\r\n    const [formData, setFormData] = useState(data);\r\n    const [error, setError] = useState(\"\");\r\n\r\n    const handleChange = (event) => {\r\n        const { name, value } = event.target;\r\n        setFormData((prevData) => ({\r\n            ...prevData,\r\n            [name]: value,\r\n        }));\r\n        setError(\"\");\r\n    };\r\n\r\n    useEffect(() => {\r\n        validateForm();\r\n    }, [formData]);\r\n\r\n    const handlePrevious = (e) => {\r\n        e.preventDefault();\r\n        onPrevious(formData);\r\n    };\r\n\r\n    const handleNext = (e) => {\r\n        e.preventDefault();\r\n        if (validateForm()) {\r\n            onNext(formData);\r\n        } else {\r\n            toast.error(\"Please validate your information\", {\r\n                position: \"top-center\",\r\n                autoClose: 1000,\r\n            });\r\n        }\r\n    };\r\n\r\n    const validateForm = () => {\r\n        const { contactInfo } = formData;\r\n\r\n        const isPhoneNumberValid =\r\n            /^\\d{8}$/.test(contactInfo) && !/(\\d)\\1{7,}/.test(contactInfo);\r\n\r\n        setError(\r\n            contactInfo.trim() !== \"\"\r\n                ? isPhoneNumberValid\r\n                    ? \"\"\r\n                    : \"Phone number is Invalid\"\r\n                : \"Phone number is required\"\r\n        );\r\n\r\n        return isPhoneNumberValid;\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <Stack spacing={3} className=\"mb-5\">\r\n                <TextField\r\n                    id=\"contactInfo\"\r\n                    name=\"contactInfo\"\r\n                    label=\"Phone number\"\r\n                    type=\"tel\"\r\n                    value={formData.contactInfo}\r\n                    onChange={handleChange}\r\n                    error={error && formData.contactInfo.trim() !== \"\"}\r\n                    helperText={error}\r\n                />\r\n            </Stack>\r\n\r\n            <Typography variant=\"body2\" sx={{ mb: 5 }}>\r\n                Already have an account?{\" \"}\r\n                <a href=\"/login\" style={{ color: \"silver\" }}>\r\n                    Sign in\r\n                </a>\r\n            </Typography>\r\n\r\n            <LoadingButton\r\n                className=\"me-5\"\r\n                size=\"large\"\r\n                type=\"submit\"\r\n                variant=\"contained\"\r\n                onClick={handlePrevious}\r\n            >\r\n                Previous\r\n            </LoadingButton>\r\n            <LoadingButton\r\n                size=\"large\"\r\n                type=\"submit\"\r\n                variant=\"contained\"\r\n                onClick={handleNext}\r\n                disabled={!!error}\r\n            >\r\n                Next\r\n            </LoadingButton>\r\n            <ToastContainer />\r\n        </>\r\n    );\r\n};\r\n\r\nexport default Register2;\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\auth\\signup\\RegisterConfirm.js", ["721"], [], "import { React, useEffect } from \"react\";\r\n\r\nconst RegisterConfirm = ({ onSubmit }) => {\r\n    useEffect(() => {\r\n        onSubmit();\r\n    }, []);\r\n    return <></>;\r\n};\r\n\r\nexport default RegisterConfirm;\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\auth\\signup\\Register1.js", ["722", "723"], [], "import React, { useState, useEffect } from \"react\";\r\nimport {\r\n    Stack,\r\n    FormControl,\r\n    RadioGroup,\r\n    TextField,\r\n    FormControlLabel,\r\n    Radio,\r\n    Dialog,\r\n    DialogActions,\r\n    DialogContent,\r\n    DialogContentText,\r\n    DialogTitle,\r\n    Button,\r\n    Typography,\r\n} from \"@mui/material\";\r\nimport { LoadingButton } from \"@mui/lab\";\r\nimport { ToastContainer, toast } from \"react-toastify\";\r\nimport \"./Register1.css\";\r\n\r\nconst Register1 = ({ onNext, data }) => {\r\n    const [formData, setData] = useState(data);\r\n    const [usernameChanged, setUsernameChanged] = useState(false);\r\n    const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);\r\n    const [isFormValid, setFormValid] = useState(false);\r\n    const [errors, setErrors] = useState({\r\n        firstName: \"\",\r\n        lastName: \"\",\r\n        userName: \"\",\r\n    });\r\n\r\n    const handleChange = (event) => {\r\n        const { name, value } = event.target;\r\n        setData((prevData) => ({\r\n            ...prevData,\r\n            [name]: value,\r\n        }));\r\n    };\r\n\r\n    useEffect(() => {\r\n        validateForm();\r\n    }, [formData]);\r\n\r\n    const handleSubmit = (e) => {\r\n        e.preventDefault();\r\n        if (isFormValid) {\r\n            if (usernameChanged) {\r\n                handleConfirmDialogOpen();\r\n            } else {\r\n                onNext(formData);\r\n            }\r\n        } else {\r\n            toast.error(\r\n                \"Please validate your information and select a gender\",\r\n                {\r\n                    position: \"top-center\",\r\n                    autoClose: 1000,\r\n                }\r\n            );\r\n        }\r\n    };\r\n\r\n    const validateForm = () => {\r\n        const { firstName, lastName, userName, gender } = formData;\r\n\r\n        const isFirstNameValid = /^[A-Za-z ]{3,32}$/.test(firstName);\r\n        const isLastNameValid = /^[A-Za-z ]{3,32}$/.test(lastName);\r\n        const isUserNameValid = /^[A-Za-z0-9_]{3,16}$/.test(userName);\r\n        const isGenderSelected = gender.trim() !== \"\";\r\n\r\n        setErrors({\r\n            firstName:\r\n                firstName.length < 3\r\n                    ? \"First name is required ( minimum 3 characters )\"\r\n                    : isFirstNameValid\r\n                    ? \"\"\r\n                    : \"Invalid first name\",\r\n            lastName:\r\n                lastName.length < 3\r\n                    ? \"Last name is required ( minimum 3 characters )\"\r\n                    : isLastNameValid\r\n                    ? \"\"\r\n                    : \"Invalid last name\",\r\n            userName:\r\n                userName.length < 3\r\n                    ? \"Username is required ( minimum 3 characters )\"\r\n                    : isUserNameValid\r\n                    ? \"\"\r\n                    : \"Username must be 3-16 characters and can only contain letters, numbers, and underscores\",\r\n            gender: isGenderSelected ? \"\" : \"Please select a gender\",\r\n        });\r\n\r\n        setFormValid(\r\n            isFirstNameValid &&\r\n                isLastNameValid &&\r\n                isUserNameValid &&\r\n                isGenderSelected\r\n        );\r\n    };\r\n\r\n    const handleConfirmDialogOpen = () => {\r\n        setConfirmDialogOpen(true);\r\n    };\r\n\r\n    const handleConfirmDialogClose = () => {\r\n        setConfirmDialogOpen(false);\r\n    };\r\n\r\n    const handleUsernameChangeConfirmed = () => {\r\n        setConfirmDialogOpen(false);\r\n        onNext(formData);\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <form>\r\n                <Stack spacing={3}>\r\n                    <TextField\r\n                        id=\"firstName\"\r\n                        name=\"firstName\"\r\n                        label=\"First Name\"\r\n                        value={formData.firstName}\r\n                        onChange={handleChange}\r\n                        required\r\n                        error={\r\n                            errors.firstName && formData.firstName.trim() !== \"\"\r\n                        }\r\n                        helperText={errors.firstName}\r\n                    />\r\n\r\n                    <TextField\r\n                        id=\"lastName\"\r\n                        name=\"lastName\"\r\n                        label=\"Last Name\"\r\n                        value={formData.lastName}\r\n                        onChange={handleChange}\r\n                        required\r\n                        error={\r\n                            errors.lastName && formData.lastName.trim() !== \"\"\r\n                        }\r\n                        helperText={errors.lastName}\r\n                    />\r\n\r\n                    <TextField\r\n                        id=\"userName\"\r\n                        name=\"userName\"\r\n                        label=\"Username\"\r\n                        value={formData.userName}\r\n                        onChange={handleChange}\r\n                        required\r\n                        error={\r\n                            errors.userName && formData.userName.trim() !== \"\"\r\n                        }\r\n                        helperText={errors.userName}\r\n                    />\r\n\r\n                    <p style={{ color: \"red\" }}>\r\n                        Note: Usernames are permanent and cannot be changed\r\n                        after registration. Choose wisely!\r\n                    </p>\r\n\r\n                    <FormControl className=\"ms-5 mb-4\">\r\n                        <RadioGroup\r\n                            row\r\n                            aria-labelledby=\"col-sm-3\"\r\n                            name=\"gender\"\r\n                            value={formData.gender}\r\n                            onChange={handleChange}\r\n                        >\r\n                            <FormControlLabel\r\n                                value=\"female\"\r\n                                label={\"Female\"}\r\n                                control={<Radio />}\r\n                            />\r\n                            <FormControlLabel\r\n                                value=\"male\"\r\n                                label={\"Male\"}\r\n                                control={<Radio />}\r\n                            />\r\n                        </RadioGroup>\r\n                    </FormControl>\r\n                </Stack>\r\n\r\n                <Typography variant=\"body2\" sx={{ mb: 5 }}>\r\n                    Already have an account?{\" \"}\r\n                    <a href=\"/login\" style={{ color: \"silver\" }}>\r\n                        Sign in\r\n                    </a>\r\n                </Typography>\r\n\r\n                <LoadingButton\r\n                    size=\"large\"\r\n                    type=\"submit\"\r\n                    variant=\"contained\"\r\n                    onClick={handleSubmit}\r\n                    disabled={!isFormValid}\r\n                >\r\n                    Next\r\n                </LoadingButton>\r\n\r\n                <ToastContainer />\r\n            </form>\r\n\r\n            {/* Confirmation Dialog */}\r\n            <Dialog\r\n                open={confirmDialogOpen}\r\n                onClose={handleConfirmDialogClose}\r\n                aria-labelledby=\"alert-dialog-title\"\r\n                aria-describedby=\"alert-dialog-description\"\r\n            >\r\n                <DialogTitle id=\"alert-dialog-title\">\r\n                    {\"Confirm Username Change\"}\r\n                </DialogTitle>\r\n                <DialogContent>\r\n                    <DialogContentText id=\"alert-dialog-description\">\r\n                        your username \"{formData.userName}\" is a permanent\r\n                        action. <br /> Are you sure you want to proceed?\r\n                    </DialogContentText>\r\n                </DialogContent>\r\n                <DialogActions>\r\n                    <Button onClick={handleConfirmDialogClose} color=\"primary\">\r\n                        Cancel\r\n                    </Button>\r\n                    <Button\r\n                        onClick={handleUsernameChangeConfirmed}\r\n                        color=\"primary\"\r\n                        autoFocus\r\n                    >\r\n                        Confirm\r\n                    </Button>\r\n                </DialogActions>\r\n            </Dialog>\r\n        </>\r\n    );\r\n};\r\n\r\nexport default Register1;\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Contact\\ContactSection.js", ["724", "725"], [], "import React, { useState, useEffect, useRef } from \"react\";\r\nimport { Worker, Viewer } from \"@react-pdf-viewer/core\";\r\nimport \"@react-pdf-viewer/core/lib/styles/index.css\";\r\nimport {\r\n    Grid,\r\n    <PERSON>,\r\n    Typography,\r\n    Button,\r\n    Dialog,\r\n    Box,\r\n    DialogContent,\r\n    DialogTitle,\r\n    CircularProgress,\r\n    IconButton,\r\n    CardContent,\r\n} from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport PortraitIcon from \"@mui/icons-material/Portrait\";\r\n\r\nconst ContactSection = ({ account }) => {\r\n    const [isCvFileFound, setIsCVFileFound] = useState(false);\r\n    const [isLoading, setIsLoading] = useState(true);\r\n    const [dialogOpen, setDialogOpen] = useState(false);\r\n    const fileURLRef = useRef(null);\r\n\r\n    useEffect(() => {\r\n        const existingCvContact = account.contacts.find(\r\n            (contact) => contact.category === \"CvFile\"\r\n        );\r\n        if (existingCvContact) {\r\n            setIsCVFileFound(true);\r\n            fileURLRef.current = existingCvContact.contactInfo;\r\n        }\r\n        setIsLoading(false);\r\n    }, [account.contacts]);\r\n\r\n    const handleDownload = () => {\r\n        if (fileURLRef.current) {\r\n            const link = document.createElement(\"a\");\r\n            console.log(fileURLRef.current);\r\n            link.href = `data:application/pdf;base64,${fileURLRef.current}`;\r\n            link.download = `${account.firstName}_${account.lastName}_CV.pdf`;\r\n            link.click();\r\n        }\r\n    };\r\n\r\n    return (\r\n        <>\r\n            {isCvFileFound && (\r\n                <Grid item xs={12} md={12}>\r\n                    <Card sx={{ display: \"flex\", marginTop: \"20px\" }}>\r\n                        <Box\r\n                            sx={{\r\n                                objectFit: \"cover\",\r\n                                height: \"100%\",\r\n                                width: \"560px\",\r\n                                display: { xs: \"none\", sm: \"block\" },\r\n                                overflow: \"hidden\",\r\n                            }}\r\n                        >\r\n                            <img\r\n                                src=\"../assets/images/PublicCv.jpg\"\r\n                                style={{\r\n                                    width: \"100%\",\r\n                                    height: \"100%\",\r\n                                    objectFit: \"cover\",\r\n                                }}\r\n                            />\r\n                        </Box>\r\n\r\n                        <CardContent sx={{ textAlign: \"left\" }}>\r\n                            <Typography\r\n                                variant=\"overline\"\r\n                                sx={{\r\n                                    mb: 2,\r\n                                    color: \"text.secondary\",\r\n                                    fontSize: \"1rem\",\r\n                                    letterSpacing: \"0.1rem\",\r\n                                    textTransform: \"uppercase\",\r\n                                    fontWeight: \"bold\",\r\n                                }}\r\n                            >\r\n                                Resume / CV\r\n                            </Typography>\r\n                            <Typography\r\n                                variant=\"h5\"\r\n                                sx={{\r\n                                    fontWeight: \"bold\",\r\n                                    color: \"#333\",\r\n                                    marginBottom: \"12px\",\r\n                                }}\r\n                            >\r\n                                {account.firstName} {account.lastName}'s\r\n                                Professional Resume\r\n                            </Typography>\r\n                            <Typography\r\n                                variant=\"body2\"\r\n                                color=\"text.secondary\"\r\n                                sx={{\r\n                                    lineHeight: \"1.75\",\r\n                                    marginBottom: \"24px\",\r\n                                }}\r\n                            >\r\n                                Discover the full range of {account.firstName}'s\r\n                                experiences, skills, and qualifications. Click\r\n                                below to view the complete resume in PDF format.\r\n                            </Typography>\r\n                            <Box\r\n                                sx={{\r\n                                    display: \"flex\",\r\n                                    justifyContent: \"space-between\",\r\n                                }}\r\n                            >\r\n                                <Button\r\n                                    variant=\"contained\"\r\n                                    color=\"primary\"\r\n                                    onClick={() => setDialogOpen(true)}\r\n                                    sx={{ borderRadius: \"8px\" }}\r\n                                >\r\n                                    <PortraitIcon />\r\n                                </Button>\r\n                                {/* <Button\r\n                                    variant=\"outlined\"\r\n                                    color=\"primary\"\r\n                                    onClick={handleDownload}\r\n                                    sx={{ borderRadius: \"8px\" }}\r\n                                >\r\n                                    Download CV\r\n                                </Button> */}\r\n                            </Box>\r\n                        </CardContent>\r\n                    </Card>\r\n                    <Dialog\r\n                        open={dialogOpen}\r\n                        onClose={() => setDialogOpen(false)}\r\n                        fullWidth\r\n                        maxWidth=\"md\"\r\n                    >\r\n                        <DialogTitle>\r\n                            CV Preview <PortraitIcon />\r\n                        </DialogTitle>\r\n                        <DialogContent>\r\n                            <IconButton\r\n                                sx={{\r\n                                    position: \"absolute\",\r\n                                    right: 8,\r\n                                    top: 8,\r\n                                }}\r\n                                aria-label=\"close\"\r\n                                onClick={() => setDialogOpen(false)}\r\n                            >\r\n                                <CloseIcon />\r\n                            </IconButton>\r\n                            {isLoading ? (\r\n                                <CircularProgress />\r\n                            ) : (\r\n                                <div\r\n                                    style={{\r\n                                        height: \"600px\",\r\n                                        width: \"100%\",\r\n                                        overflow: \"auto\",\r\n                                    }}\r\n                                >\r\n                                    <Worker\r\n                                        workerUrl={`https://unpkg.com/pdfjs-dist@2.15.349/build/pdf.worker.min.js`}\r\n                                    >\r\n                                        <Viewer\r\n                                            fileUrl={fileURLRef.current}\r\n                                            showPreviousViewOnLoad={false}\r\n                                        />\r\n                                    </Worker>\r\n                                </div>\r\n                            )}\r\n                        </DialogContent>\r\n                    </Dialog>\r\n                </Grid>\r\n            )}\r\n        </>\r\n    );\r\n};\r\n\r\nexport default ContactSection;\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppTrafficBySite.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppLinksByPublicProfile.js", ["726"], [], "import { Box, Paper, Typography, Avatar } from \"@mui/material\";\r\nimport { PostClick } from \"../../../AnalyticsData.ts\";\r\nimport { useNavigate } from \"react-router-dom\";\r\n\r\nexport default function AppLinksByPublicProfile({\r\n  AccountId,\r\n  list,\r\n  profile,\r\n  type,\r\n  sameAccount = false,\r\n}) {\r\n  const navigate = useNavigate();\r\n\r\n  const handleClick = async (key, link) => {\r\n    // Add a small delay to ensure touch event is properly registered on iOS\r\n    setTimeout(async () => {\r\n      if (sameAccount === false) {\r\n        await PostClick({\r\n          linkId: key,\r\n          userId: AccountId ?? null,\r\n          gender: profile.profile.gender ?? null,\r\n          country: profile.country ?? null,\r\n          category: link.category,\r\n          date: new Date(),\r\n        });\r\n      }\r\n      if (validateURL(link.body)) {\r\n        // Use a more iOS-friendly approach\r\n        if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {\r\n          window.location.href = link.body;\r\n        } else {\r\n          window.open(link.body, \"_blank\");\r\n        }\r\n      } else {\r\n        navigate(\"/404\");\r\n      }\r\n    }, 100); // Small delay to ensure touch event completes\r\n  };\r\n\r\n  const validateURL = (url) => {\r\n    try {\r\n      new URL(url);\r\n      return true;\r\n    } catch (e) {\r\n      return false;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Box>\r\n      {type == \"socialLinks\"\r\n        ? list.map((site, key) => (\r\n            <Paper\r\n              sx={{\r\n                padding: \"0.8rem 1.5rem\",\r\n                borderRadius: \"10px\",\r\n                background: \"#f5f5f5f5\",\r\n                cursor: \"pointer\",\r\n                display: \"flex\",\r\n                justifyContent: \"center\", // S\r\n                margin: \"0.6rem 0\",\r\n                // border: `2px solid ${site.color}`,\r\n                marginTop: \"15px\",\r\n                boxShadow: \"0 0.4rem 0.8rem rgba(20, 43, 58, 0.50)\",\r\n                transition: \"transform 0.3s ease-in-out\",\r\n                \"&:hover\": {\r\n                  transform: \"scale(1.05)\", // Apply scale transformation on hover\r\n                },\r\n                // iOS touch improvements\r\n                WebkitTapHighlightColor: \"rgba(0, 0, 0, 0.1)\",\r\n                WebkitTouchCallout: \"none\",\r\n                WebkitUserSelect: \"none\",\r\n                userSelect: \"none\",\r\n                touchAction: \"manipulation\",\r\n                minHeight: \"44px\", // Ensure minimum touch target size for iOS\r\n              }}\r\n              key={key}\r\n              onClick={() => handleClick(key, site)}\r\n            >\r\n              <Box color={site.color} fontSize=\"18px\">\r\n                {site.icon}\r\n              </Box>\r\n\r\n              <Typography\r\n                sx={{\r\n                  color: \"#333333\",\r\n                  fontWeight: 1000,\r\n                  margin: \"auto\",\r\n                }}\r\n              >\r\n                {site.name}\r\n              </Typography>\r\n            </Paper>\r\n          ))\r\n        : list.map((site, key) => (\r\n            <Paper\r\n              sx={{\r\n                padding: \"0.8rem 1.5rem\",\r\n                borderRadius: \"10px\",\r\n                position: \"relative\",\r\n                background: \"#f5f5f5f5\",\r\n                cursor: \"pointer\",\r\n                display: \"flex\",\r\n                justifyContent: \"center\",\r\n                margin: \"0.6rem 0\",\r\n                marginTop: \"15px\",\r\n                boxShadow: \"0 0.4rem 0.8rem rgba(20, 43, 58, 0.50)\",\r\n                transition: \"transform 0.3s ease-in-out\",\r\n                \"&:hover\": {\r\n                  transform: \"scale(1.05)\",\r\n                },\r\n                \"&::after\": {\r\n                  content: '\"\"',\r\n                  position: \"absolute\",\r\n                  top: 0,\r\n                  right: 0,\r\n                  bottom: 0,\r\n                  width: \"10px\",\r\n                  backgroundColor: \"#ff715b\",\r\n                  borderTopRightRadius: \"10px\",\r\n                  borderBottomRightRadius: \"10px\",\r\n                },\r\n                // iOS touch improvements\r\n                WebkitTapHighlightColor: \"rgba(0, 0, 0, 0.1)\",\r\n                WebkitTouchCallout: \"none\",\r\n                WebkitUserSelect: \"none\",\r\n                userSelect: \"none\",\r\n                touchAction: \"manipulation\",\r\n                minHeight: \"44px\", // Ensure minimum touch target size for iOS\r\n              }}\r\n              onClick={() => handleClick(key, site.body)}\r\n              key={key}\r\n            >\r\n              <Avatar\r\n                style={{\r\n                  width: \"40px\",\r\n                  height: \"40px\",\r\n                  borderRadius: \"60%\",\r\n                  border: \"1px solid rgba(10, 20, 20, 0.12)\",\r\n                  marginBottom: \"0.2rem\",\r\n                }}\r\n                src={site.icon}\r\n              />\r\n\r\n              <Typography\r\n                sx={{\r\n                  color: \"#333333\",\r\n                  fontWeight: 1000,\r\n                  fontSize: \"15px\",\r\n                  margin: \"auto\",\r\n                }}\r\n              >\r\n                {site.name}\r\n              </Typography>\r\n            </Paper>\r\n          ))}\r\n    </Box>\r\n  );\r\n}\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppOrderTimeline.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppCurrentSubject.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppProfileCard.js", ["727"], [], "import React from \"react\";\r\nimport { Box, Paper, Avatar } from \"@mui/material\";\r\nimport { styled } from \"@mui/material/styles\";\r\nimport \"./appprofilecard.css\";\r\n\r\nconst StyledCover = styled(\"img\")({\r\n    top: 0,\r\n    width: \"100%\",\r\n    height: \"100%\",\r\n    objectFit: \"cover\",\r\n    position: \"absolute\",\r\n});\r\n\r\nexport default function AppProfileCard({ User, Profile }) {\r\n    if (!User) {\r\n        return <div>Loading user data...</div>;\r\n    }\r\n\r\n    return (\r\n        <Box\r\n            sx={{\r\n                display: \"grid\",\r\n                gap: 3,\r\n            }}\r\n            className=\"mobile-container\"\r\n        >\r\n            <Paper\r\n                sx={{\r\n                    textAlign: \"center\",\r\n                    fontSize: \"13px\",\r\n                }}\r\n            >\r\n                {/* profile picture */}\r\n                <div\r\n                    className=\"rounded-top text-white\"\r\n                    style={{\r\n                        display: \"flex\",\r\n                        justifyContent: \"center\",\r\n                    }}\r\n                >\r\n                    <Avatar\r\n                        alt=\"Profile\"\r\n                        src={Profile.profilePicture}\r\n                        sx={{\r\n                            marginTop: \"20px\",\r\n                            width: 130,\r\n                            height: 130,\r\n                        }}\r\n                    />\r\n                </div>\r\n                {/* name / occupation / username */}\r\n                <div className=\"p-4\">\r\n                    <div className=\"text-center\">\r\n                        <div>\r\n                            <p className=\"mb profile-name\">\r\n                                {User.firstName} {User.lastName}\r\n                            </p>\r\n                            <p className=\"text-muted mb-0\">\r\n                                @{Profile.userName}\r\n                            </p>\r\n                            <p className=\"text-muted mb-0\">\r\n                                {Profile.occupation ? Profile.occupation : \"-\"}\r\n                            </p>\r\n                            <p className=\"text-muted mb-0\">\r\n                                {Profile.gender ? Profile.gender : \"-\"}\r\n                            </p>\r\n                        </div>\r\n                        {/* <div className=\"px-3\">\r\n                                    <p className=\"mb-1 h6\">\r\n                                        {Profile.occupation\r\n                                            ? Profile.occupation\r\n                                            : \"-\"}\r\n                                    </p>\r\n                                    <p className=\"small text-muted mb-0\">\r\n                                        Occupation\r\n                                    </p>\r\n                                </div>\r\n                                <div className=\"px-3\">\r\n                                    <p className=\"mb-1 h6\">\r\n                                        {Profile.gender ? Profile.gender : \"-\"}\r\n                                    </p>\r\n                                    <p className=\"small text-muted mb-0\">\r\n                                        Gender\r\n                                    </p>\r\n                                </div> */}\r\n                    </div>\r\n                </div>\r\n            </Paper>\r\n        </Box>\r\n    );\r\n}\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\app\\AppBundleWidget.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\auth\\signup\\Register3.js", ["728", "729", "730"], [], "import React, { useState, useEffect } from \"react\";\r\nimport {\r\n    Stack,\r\n    IconButton,\r\n    InputAdornment,\r\n    TextField,\r\n    Avatar,\r\n    Typography,\r\n} from \"@mui/material\";\r\nimport { LoadingButton } from \"@mui/lab\";\r\nimport Iconify from \"../../../components/iconify\";\r\nimport PhotoSelector from \"./PhotoSelector\";\r\nimport { ToastContainer, toast } from \"react-toastify\";\r\nimport \"react-toastify/dist/ReactToastify.css\";\r\nimport \"./Register3.css\";\r\n\r\nconst Register3 = ({ onNext, onPrevious, data }) => {\r\n    const [formData, setFormData] = useState(data);\r\n    const [showPassword, setShowPassword] = useState(false);\r\n    const [strength, setStrength] = useState(\"\");\r\n    const [emailError, setEmailError] = useState(\"\");\r\n    const [passwordError, setPasswordError] = useState(\"\");\r\n\r\n    const handleChange = (event) => {\r\n        const { name, value } = event.target;\r\n        setFormData((prevData) => ({\r\n            ...prevData,\r\n            [name]: value,\r\n        }));\r\n\r\n        if (name === \"password\") validatePassword(value);\r\n    };\r\n\r\n    const handlePhotoSelect = (photoDataUrl) => {\r\n        setFormData((prevData) => ({\r\n            ...prevData,\r\n            profilePicture: photoDataUrl,\r\n        }));\r\n    };\r\n\r\n    const handlePrevious = (e) => {\r\n        e.preventDefault();\r\n        onPrevious(formData);\r\n    };\r\n\r\n    const handleNext = (e) => {\r\n        e.preventDefault();\r\n\r\n        validateEmail(formData.email);\r\n        validatePassword(formData.password);\r\n\r\n        if (!emailError && !passwordError) {\r\n            onNext(formData);\r\n        } else {\r\n            toast.error(`Please validate your information `, {\r\n                position: \"top-center\",\r\n                autoClose: 1000,\r\n            });\r\n        }\r\n    };\r\n\r\n    const validateEmail = (email) => {\r\n        const isValidEmail = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email);\r\n        setEmailError(isValidEmail ? \"\" : \"Invalid email\");\r\n    };\r\n\r\n    const validatePassword = (password) => {\r\n        let strengthIndicator = -1;\r\n        let upper = false,\r\n            lower = false,\r\n            numbers = false,\r\n            firstname = false,\r\n            lastname = false,\r\n            username = false,\r\n            specialChars = false;\r\n\r\n        if (/[A-Z]/.test(password)) upper = true;\r\n        if (/[a-z]/.test(password)) lower = true;\r\n        if (/\\d/.test(password)) numbers = true;\r\n        if (/[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(password))\r\n            specialChars = true;\r\n        if (\r\n            formData.password\r\n                .toLowerCase()\r\n                .trim()\r\n                .includes(formData.firstName.toLowerCase().trim())\r\n        )\r\n            firstname = true;\r\n        if (\r\n            formData.password\r\n                .toLowerCase()\r\n                .trim()\r\n                .includes(formData.lastName.toLowerCase().trim())\r\n        )\r\n            lastname = true;\r\n        if (\r\n            formData.password\r\n                .toLowerCase()\r\n                .trim()\r\n                .includes(formData.userName.toLowerCase().trim())\r\n        )\r\n            username = true;\r\n\r\n        if (password.length >= 8) {\r\n            strengthIndicator++;\r\n            if (!firstname && !lastname && !username && password.length > 5)\r\n                strengthIndicator++;\r\n        }\r\n        if (upper && lower && numbers && specialChars) strengthIndicator++;\r\n\r\n        setStrength([\"weak\", \"medium\", \"strong\"][strengthIndicator] || \"\");\r\n\r\n        switch (true) {\r\n            case password.length < 8:\r\n                setPasswordError(\r\n                    \"Password must be at least 8 characters long.\"\r\n                );\r\n                break;\r\n            case firstname || lastname:\r\n                setPasswordError(\r\n                    \"Password must not match your first or last name.\"\r\n                );\r\n                break;\r\n            case username:\r\n                setPasswordError(\r\n                    \"Password must not match your unique user name.\"\r\n                );\r\n                break;\r\n            case !specialChars:\r\n                setPasswordError(\r\n                    \"Password must contain at least one special character.\"\r\n                );\r\n                break;\r\n            case !upper:\r\n                setPasswordError(\r\n                    \"Password must contain at least one upper case character.\"\r\n                );\r\n                break;\r\n            case !lower:\r\n                setPasswordError(\r\n                    \"Password must contain at least one lower case character.\"\r\n                );\r\n                break;\r\n            case !numbers:\r\n                setPasswordError(\"Password must contain numbers.\");\r\n                break;\r\n            default:\r\n                setPasswordError(\"\");\r\n                break;\r\n        }\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <Stack spacing={3}>\r\n                <TextField\r\n                    id=\"email\"\r\n                    name=\"email\"\r\n                    label=\"Email\"\r\n                    value={formData.email}\r\n                    onChange={handleChange}\r\n                    error={!!emailError}\r\n                    helperText={emailError}\r\n                    aria-invalid={!!emailError}\r\n                    aria-describedby=\"email-error\"\r\n                />\r\n                <TextField\r\n                    id=\"password\"\r\n                    name=\"password\"\r\n                    label=\"Password\"\r\n                    value={formData.password}\r\n                    type={showPassword ? \"text\" : \"password\"}\r\n                    InputProps={{\r\n                        endAdornment: (\r\n                            <InputAdornment position=\"end\">\r\n                                <IconButton\r\n                                    onClick={() =>\r\n                                        setShowPassword(!showPassword)\r\n                                    }\r\n                                    edge=\"end\"\r\n                                >\r\n                                    {showPassword ? (\r\n                                        <Iconify icon=\"eva:eye-fill\" />\r\n                                    ) : (\r\n                                        <Iconify icon=\"eva:eye-off-fill\" />\r\n                                    )}\r\n                                </IconButton>\r\n                            </InputAdornment>\r\n                        ),\r\n                    }}\r\n                    onChange={handleChange}\r\n                    error={!!passwordError}\r\n                    helperText={passwordError}\r\n                    aria-invalid={!!passwordError}\r\n                    aria-describedby=\"password-error\"\r\n                />\r\n                <div className={`bars ${strength}`}>\r\n                    <div></div>\r\n                </div>\r\n                <div className=\"row mb-5\">\r\n                    <div className=\"col\">\r\n                        <div className=\"photo-selector-label\">\r\n                            Profile Picture\r\n                        </div>\r\n                        <Avatar\r\n                            src={formData.profilePicture}\r\n                            alt=\"User Profile Photo\"\r\n                        />\r\n                        <PhotoSelector onSelect={handlePhotoSelect} />\r\n                    </div>\r\n                </div>\r\n            </Stack>\r\n\r\n            <Typography variant=\"body2\" sx={{ mb: 5 }}>\r\n                Already have an account?{\" \"}\r\n                <a href=\"/login\" color=\"primary\">\r\n                    Sign in\r\n                </a>\r\n            </Typography>\r\n\r\n            <LoadingButton\r\n                className=\"me-5\"\r\n                size=\"large\"\r\n                type=\"submit\"\r\n                variant=\"contained\"\r\n                onClick={handlePrevious}\r\n            >\r\n                Previous\r\n            </LoadingButton>\r\n            <LoadingButton\r\n                size=\"large\"\r\n                type=\"submit\"\r\n                variant=\"contained\"\r\n                onClick={handleNext}\r\n                disabled={\r\n                    !!emailError ||\r\n                    !!passwordError ||\r\n                    strength.trim() !== \"strong\"\r\n                }\r\n            >\r\n                Next\r\n            </LoadingButton>\r\n            <ToastContainer />\r\n        </>\r\n    );\r\n};\r\n\r\nexport default Register3;\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\layouts\\dashboard\\header\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\layouts\\dashboard\\nav\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\auth\\login\\LoginForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\iconify\\Iconify.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\Scrollbar.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\layouts\\dashboard\\header\\AccountPopover.js", ["731"], [], "import { useState, useEffect } from \"react\";\r\n// @mui\r\nimport {\r\n  Box,\r\n  Divider,\r\n  Typography,\r\n  Stack,\r\n  MenuItem,\r\n  IconButton,\r\n  Popover,\r\n} from \"@mui/material\";\r\n// mocks_\r\nimport { Logout, checkAuthToken } from \"../../../AuthenticationData.ts\";\r\nimport { useProfile } from \"../../../Context/ProfileContext\";\r\nimport { useNavigate } from \"react-router-dom\";\r\n\r\nexport default function AccountPopover() {\r\n  const { profile } = useProfile();\r\n  const [Account, setAccount] = useState({\r\n    email: \"\",\r\n    firstName: \"\",\r\n    lastName: \"\",\r\n  });\r\n  const [open, setOpen] = useState(null);\r\n  const navigate = useNavigate();\r\n\r\n  const HandleLogout = async () => {\r\n    try {\r\n      await Logout();\r\n\r\n      let isAuthenticated = checkAuthToken();\r\n\r\n      if (!isAuthenticated) {\r\n        navigate(\"/Login\");\r\n      } else {\r\n        console.error(\"Logout failed: User is still authenticated.\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error during logout:\", error);\r\n    }\r\n  };\r\n  const handleOpen = (event) => {\r\n    setOpen(event.currentTarget);\r\n  };\r\n\r\n  const handleClose = () => {\r\n    setOpen(null);\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchProfileData();\r\n  }, [profile]);\r\n\r\n  const fetchProfileData = async () => {\r\n    try {\r\n      setAccount({\r\n        email: profile.email,\r\n        firstName: profile.firstName,\r\n        lastName: profile.lastName,\r\n      });\r\n    } catch (error) {\r\n      if (error.redirectToLogin) {\r\n        navigate(\"/Login\");\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <IconButton onClick={handleOpen}>\r\n        <i className=\"bi bi-gear-fill\"></i>\r\n      </IconButton>\r\n\r\n      <Popover\r\n        open={Boolean(open)}\r\n        anchorEl={open}\r\n        onClose={handleClose}\r\n        anchorOrigin={{ vertical: \"bottom\", horizontal: \"right\" }}\r\n        transformOrigin={{ vertical: \"top\", horizontal: \"right\" }}\r\n        PaperProps={{\r\n          sx: {\r\n            mt: 1.5,\r\n            ml: 0.75,\r\n            width: 235,\r\n            \"& .MuiMenuItem-root\": {\r\n              typography: \"body2\",\r\n              borderRadius: 0.75,\r\n            },\r\n          },\r\n        }}\r\n      >\r\n        <Box sx={{ my: 1.5, px: 2.5 }}>\r\n          <Typography variant=\"subtitle2\" noWrap>\r\n            {Account.firstName} {Account.lastName}\r\n          </Typography>\r\n          <Typography variant=\"body2\" sx={{ color: \"text.secondary\" }} noWrap>\r\n            {Account.email}\r\n          </Typography>\r\n        </Box>\r\n\r\n        <Divider sx={{ borderStyle: \"dashed\" }} />\r\n\r\n        <Stack sx={{ p: 1 }}>\r\n          {/* <MenuItem onClick={handleClose}>Home</MenuItem> */}\r\n          <MenuItem\r\n            onClick={() => {\r\n              navigate(\"User\");\r\n              handleClose();\r\n            }}\r\n          >\r\n            Studio\r\n          </MenuItem>\r\n          <MenuItem\r\n            onClick={() => {\r\n              navigate(\"ManageCoupons\");\r\n              handleClose();\r\n            }}\r\n          >\r\n            Coupons\r\n          </MenuItem>\r\n          <MenuItem\r\n            onClick={() => {\r\n              navigate(\"Settings\");\r\n              handleClose();\r\n            }}\r\n          >\r\n            Settings\r\n          </MenuItem>\r\n          {/* <MenuItem onClick={handleClose}>Feedback</MenuItem> */}\r\n        </Stack>\r\n\r\n        <Divider sx={{ borderStyle: \"dashed\" }} />\r\n\r\n        <MenuItem onClick={HandleLogout} sx={{ m: 1 }}>\r\n          Logout\r\n        </MenuItem>\r\n      </Popover>\r\n    </>\r\n  );\r\n}\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\layouts\\dashboard\\header\\Searchbar.js", ["732"], [], "import { useState } from \"react\";\r\n// @mui\r\nimport { styled } from \"@mui/material/styles\";\r\nimport {\r\n    Input,\r\n    Slide,\r\n    Button,\r\n    IconButton,\r\n    InputAdornment,\r\n    ClickAwayListener,\r\n    Link,\r\n} from \"@mui/material\";\r\n// utils\r\nimport { bgBlur } from \"../../../utils/cssStyles\";\r\n// component\r\nimport Iconify from \"../../../components/iconify\";\r\n\r\nimport { toast } from \"react-toastify\";\r\nimport { useNavigate } from \"react-router-dom\";\r\n\r\n// ----------------------------------------------------------------------\r\n\r\nconst HEADER_MOBILE = 64;\r\nconst HEADER_DESKTOP = 92;\r\n\r\nconst StyledSearchbar = styled(\"div\")(({ theme }) => ({\r\n    ...bgBlur({ color: theme.palette.background.default }),\r\n    top: 0,\r\n    left: 0,\r\n    zIndex: 99,\r\n    width: \"100%\",\r\n    display: \"flex\",\r\n    position: \"absolute\",\r\n    alignItems: \"center\",\r\n    height: HEADER_MOBILE,\r\n    padding: theme.spacing(0, 3),\r\n    boxShadow: theme.customShadows.z8,\r\n    [theme.breakpoints.up(\"md\")]: {\r\n        height: HEADER_DESKTOP,\r\n        padding: theme.spacing(0, 5),\r\n    },\r\n}));\r\n\r\n// ----------------------------------------------------------------------\r\n\r\nexport default function Searchbar() {\r\n    const [open, setOpen] = useState(false);\r\n    const [searchQuery, setSearchQuery] = useState(\"\");\r\n    const navigate = useNavigate();\r\n\r\n    const handleOpen = () => {\r\n        setOpen(!open);\r\n    };\r\n\r\n    const onChange = (event) => {\r\n        setSearchQuery(event.target.value);\r\n    };\r\n\r\n    const handleClose = () => {\r\n        setOpen(false);\r\n    };\r\n\r\n    const handleSearchSubmit = async (event) => {\r\n        event.preventDefault();\r\n\r\n        const trimmedQuery = searchQuery.trim();\r\n\r\n        if (!trimmedQuery || /^\\.{3}$/.test(trimmedQuery)) {\r\n            toast.error(\"Please enter a valid search query.\", {\r\n                position: \"top-center\",\r\n                autoClose: 1000,\r\n            });\r\n        } else {\r\n            // setSearchParams({ q: trimmedQuery });\r\n            handleClose();\r\n            navigate(`/admin/search?q=${trimmedQuery}`);\r\n        }\r\n    };\r\n\r\n    const handleKeyPress = (event) => {\r\n        if (event.key === \"Enter\") {\r\n            handleSearchSubmit(event);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <ClickAwayListener onClickAway={handleClose}>\r\n            <div>\r\n                {!open && (\r\n                    <IconButton onClick={handleOpen}>\r\n                        <Iconify icon=\"eva:search-fill\" />\r\n                    </IconButton>\r\n                )}\r\n\r\n                <Slide direction=\"down\" in={open} mountOnEnter unmountOnExit>\r\n                    <StyledSearchbar>\r\n                        <Input\r\n                            autoFocus\r\n                            fullWidth\r\n                            disableUnderline\r\n                            placeholder=\"Search…\"\r\n                            startAdornment={\r\n                                <InputAdornment position=\"start\">\r\n                                    <Iconify\r\n                                        icon=\"eva:search-fill\"\r\n                                        sx={{\r\n                                            color: \"text.disabled\",\r\n                                            width: 20,\r\n                                            height: 20,\r\n                                        }}\r\n                                    />\r\n                                </InputAdornment>\r\n                            }\r\n                            sx={{ mr: 1, fontWeight: \"fontWeightBold\" }}\r\n                            onChange={onChange}\r\n                            onKeyPress={handleKeyPress}\r\n                        />\r\n                        <Button\r\n                            variant=\"contained\"\r\n                            onClick={handleSearchSubmit}\r\n                        >\r\n                            Search\r\n                        </Button>\r\n                    </StyledSearchbar>\r\n                </Slide>\r\n            </div>\r\n        </ClickAwayListener>\r\n    );\r\n}\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\layouts\\dashboard\\header\\PalastinePopover.js", ["733"], [], "import { useState } from \"react\";\r\n// @mui\r\nimport { alpha } from \"@mui/material/styles\";\r\nimport { Box, Typography, Stack, IconButton, Popover } from \"@mui/material\";\r\n\r\nimport FamilyRestroomIcon from \"@mui/icons-material/FamilyRestroom\";\r\n\r\n// ----------------------------------------------------------------------\r\n\r\nconst LANGS = [\r\n    {\r\n        value: \"en\",\r\n        label: \"English\",\r\n        icon: \"/assets/icons/ic_flag_en.svg\",\r\n    },\r\n];\r\n\r\n// ----------------------------------------------------------------------\r\n\r\nexport default function PalastinePopover() {\r\n    const [open, setOpen] = useState(null);\r\n\r\n    const handleOpen = (event) => {\r\n        setOpen(event.currentTarget);\r\n    };\r\n\r\n    const handleClose = () => {\r\n        setOpen(null);\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <IconButton\r\n                onClick={handleOpen}\r\n                sx={{\r\n                    padding: 0,\r\n                    width: 44,\r\n                    height: 44,\r\n                    ...(open && {\r\n                        bgcolor: (theme) =>\r\n                            alpha(\r\n                                theme.palette.primary.main,\r\n                                theme.palette.action.focusOpacity\r\n                            ),\r\n                    }),\r\n                }}\r\n            >\r\n                <img src=\"/assets/icons/ic_flag_pl.svg\" alt=\"Free Palastine\" />\r\n            </IconButton>\r\n\r\n            <Popover\r\n                open={Boolean(open)}\r\n                anchorEl={open}\r\n                onClose={handleClose}\r\n                anchorOrigin={{ vertical: \"bottom\", horizontal: \"right\" }}\r\n                transformOrigin={{ vertical: \"top\", horizontal: \"right\" }}\r\n                PaperProps={{\r\n                    sx: {\r\n                        p: 1,\r\n                        mt: 1.5,\r\n                        ml: 0.75,\r\n                        width: 300,\r\n                        \"& .MuiMenuItem-root\": {\r\n                            px: 1,\r\n                            typography: \"body2\",\r\n                            borderRadius: 0.75,\r\n                        },\r\n                    },\r\n                }}\r\n            >\r\n                <Stack\r\n                    spacing={2}\r\n                    alignItems=\"center\"\r\n                    sx={{ textAlign: \"center\", p: 2 }}\r\n                >\r\n                    <Box display=\"flex\" justifyContent=\"center\" mt={2}>\r\n                        <Typography variant=\"h6\" marginRight={1}>\r\n                            Free Palestine\r\n                        </Typography>\r\n                        <FamilyRestroomIcon color=\"primary\" fontSize=\"small\" />\r\n                    </Box>\r\n                    <Typography variant=\"body2\">\r\n                        We stand in solidarity with the people of Palestine. Our\r\n                        thoughts and prayers are with them during these\r\n                        difficult times. May peace and justice prevail.\r\n                    </Typography>\r\n                    <Box\r\n                        display=\"flex\"\r\n                        flexWrap=\"wrap\"\r\n                        alignItems=\"center\"\r\n                        justifyContent=\"center\"\r\n                        mt={2}\r\n                        p={1}\r\n                        sx={{ backgroundColor: \"#f0f0f0\", borderRadius: 1 }}\r\n                    >\r\n                        <Typography\r\n                            variant=\"body2\"\r\n                            color=\"textSecondary\"\r\n                            sx={{ mr: 1 }}\r\n                        >\r\n                            #FreePalestine\r\n                        </Typography>\r\n                        <Typography\r\n                            variant=\"body2\"\r\n                            color=\"textSecondary\"\r\n                            sx={{ mr: 1 }}\r\n                        >\r\n                            #KeepTalking\r\n                        </Typography>\r\n                        <Typography\r\n                            variant=\"body2\"\r\n                            color=\"textSecondary\"\r\n                            sx={{ mr: 1 }}\r\n                        >\r\n                            #Gaza\r\n                        </Typography>\r\n                        <Typography\r\n                            variant=\"body2\"\r\n                            color=\"textSecondary\"\r\n                            sx={{ mr: 1 }}\r\n                        >\r\n                            #SavePalastine\r\n                        </Typography>\r\n                    </Box>\r\n                </Stack>\r\n            </Popover>\r\n        </>\r\n    );\r\n}\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\nav-section\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\nav-section\\NavSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\components\\nav-section\\styles.js", [], [], "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Link\\WhatsAppLinkDialog.js", ["734", "735", "736", "737", "738"], [], "import { useState, useEffect } from \"react\";\r\nimport {\r\n  TextField,\r\n  Button,\r\n  Dialog,\r\n  DialogTitle,\r\n  DialogContent,\r\n  DialogActions,\r\n  Box,\r\n  Typography,\r\n  Select,\r\n  MenuItem,\r\n  FormControl,\r\n  InputLabel,\r\n  Grid,\r\n  Chip,\r\n} from \"@mui/material\";\r\n\r\nimport { CreateContact, EditContact } from \"../../../ContactData.ts\";\r\n\r\nimport { toast } from \"react-toastify\";\r\n\r\n// Country codes data\r\nconst countryCodes = [\r\n  {\r\n    code: \"+1\",\r\n    country: \"United States\",\r\n    flag: \"https://flagcdn.com/w20/us.png\",\r\n  },\r\n  { code: \"+1\", country: \"Canada\", flag: \"https://flagcdn.com/w20/ca.png\" },\r\n  {\r\n    code: \"+44\",\r\n    country: \"United Kingdom\",\r\n    flag: \"https://flagcdn.com/w20/gb.png\",\r\n  },\r\n  { code: \"+49\", country: \"Germany\", flag: \"https://flagcdn.com/w20/de.png\" },\r\n  { code: \"+33\", country: \"France\", flag: \"https://flagcdn.com/w20/fr.png\" },\r\n  { code: \"+39\", country: \"Italy\", flag: \"https://flagcdn.com/w20/it.png\" },\r\n  { code: \"+34\", country: \"Spain\", flag: \"https://flagcdn.com/w20/es.png\" },\r\n  {\r\n    code: \"+31\",\r\n    country: \"Netherlands\",\r\n    flag: \"https://flagcdn.com/w20/nl.png\",\r\n  },\r\n  {\r\n    code: \"+41\",\r\n    country: \"Switzerland\",\r\n    flag: \"https://flagcdn.com/w20/ch.png\",\r\n  },\r\n  { code: \"+43\", country: \"Austria\", flag: \"https://flagcdn.com/w20/at.png\" },\r\n  { code: \"+32\", country: \"Belgium\", flag: \"https://flagcdn.com/w20/be.png\" },\r\n  { code: \"+45\", country: \"Denmark\", flag: \"https://flagcdn.com/w20/dk.png\" },\r\n  { code: \"+46\", country: \"Sweden\", flag: \"https://flagcdn.com/w20/se.png\" },\r\n  { code: \"+47\", country: \"Norway\", flag: \"https://flagcdn.com/w20/no.png\" },\r\n  { code: \"+358\", country: \"Finland\", flag: \"https://flagcdn.com/w20/fi.png\" },\r\n  { code: \"+91\", country: \"India\", flag: \"https://flagcdn.com/w20/in.png\" },\r\n  { code: \"+86\", country: \"China\", flag: \"https://flagcdn.com/w20/cn.png\" },\r\n  { code: \"+81\", country: \"Japan\", flag: \"https://flagcdn.com/w20/jp.png\" },\r\n  {\r\n    code: \"+82\",\r\n    country: \"South Korea\",\r\n    flag: \"https://flagcdn.com/w20/kr.png\",\r\n  },\r\n  { code: \"+65\", country: \"Singapore\", flag: \"https://flagcdn.com/w20/sg.png\" },\r\n  { code: \"+60\", country: \"Malaysia\", flag: \"https://flagcdn.com/w20/my.png\" },\r\n  { code: \"+66\", country: \"Thailand\", flag: \"https://flagcdn.com/w20/th.png\" },\r\n  { code: \"+84\", country: \"Vietnam\", flag: \"https://flagcdn.com/w20/vn.png\" },\r\n  {\r\n    code: \"+63\",\r\n    country: \"Philippines\",\r\n    flag: \"https://flagcdn.com/w20/ph.png\",\r\n  },\r\n  { code: \"+62\", country: \"Indonesia\", flag: \"https://flagcdn.com/w20/id.png\" },\r\n  { code: \"+61\", country: \"Australia\", flag: \"https://flagcdn.com/w20/au.png\" },\r\n  {\r\n    code: \"+64\",\r\n    country: \"New Zealand\",\r\n    flag: \"https://flagcdn.com/w20/nz.png\",\r\n  },\r\n  {\r\n    code: \"+27\",\r\n    country: \"South Africa\",\r\n    flag: \"https://flagcdn.com/w20/za.png\",\r\n  },\r\n  { code: \"+20\", country: \"Egypt\", flag: \"https://flagcdn.com/w20/eg.png\" },\r\n  { code: \"+216\", country: \"Tunisia\", flag: \"https://flagcdn.com/w20/tn.png\" },\r\n  { code: \"+234\", country: \"Nigeria\", flag: \"https://flagcdn.com/w20/ng.png\" },\r\n  { code: \"+254\", country: \"Kenya\", flag: \"https://flagcdn.com/w20/ke.png\" },\r\n  { code: \"+971\", country: \"UAE\", flag: \"https://flagcdn.com/w20/ae.png\" },\r\n  {\r\n    code: \"+966\",\r\n    country: \"Saudi Arabia\",\r\n    flag: \"https://flagcdn.com/w20/sa.png\",\r\n  },\r\n  { code: \"+974\", country: \"Qatar\", flag: \"https://flagcdn.com/w20/qa.png\" },\r\n  { code: \"+965\", country: \"Kuwait\", flag: \"https://flagcdn.com/w20/kw.png\" },\r\n  { code: \"+973\", country: \"Bahrain\", flag: \"https://flagcdn.com/w20/bh.png\" },\r\n  { code: \"+968\", country: \"Oman\", flag: \"https://flagcdn.com/w20/om.png\" },\r\n  { code: \"+972\", country: \"Israel\", flag: \"https://flagcdn.com/w20/il.png\" },\r\n  { code: \"+90\", country: \"Turkey\", flag: \"https://flagcdn.com/w20/tr.png\" },\r\n  { code: \"+7\", country: \"Russia\", flag: \"https://flagcdn.com/w20/ru.png\" },\r\n  { code: \"+380\", country: \"Ukraine\", flag: \"https://flagcdn.com/w20/ua.png\" },\r\n  { code: \"+48\", country: \"Poland\", flag: \"https://flagcdn.com/w20/pl.png\" },\r\n  {\r\n    code: \"+420\",\r\n    country: \"Czech Republic\",\r\n    flag: \"https://flagcdn.com/w20/cz.png\",\r\n  },\r\n  { code: \"+36\", country: \"Hungary\", flag: \"https://flagcdn.com/w20/hu.png\" },\r\n  { code: \"+40\", country: \"Romania\", flag: \"https://flagcdn.com/w20/ro.png\" },\r\n  { code: \"+359\", country: \"Bulgaria\", flag: \"https://flagcdn.com/w20/bg.png\" },\r\n  { code: \"+385\", country: \"Croatia\", flag: \"https://flagcdn.com/w20/hr.png\" },\r\n  { code: \"+381\", country: \"Serbia\", flag: \"https://flagcdn.com/w20/rs.png\" },\r\n  { code: \"+55\", country: \"Brazil\", flag: \"https://flagcdn.com/w20/br.png\" },\r\n  { code: \"+52\", country: \"Mexico\", flag: \"https://flagcdn.com/w20/mx.png\" },\r\n  { code: \"+54\", country: \"Argentina\", flag: \"https://flagcdn.com/w20/ar.png\" },\r\n  { code: \"+56\", country: \"Chile\", flag: \"https://flagcdn.com/w20/cl.png\" },\r\n  { code: \"+57\", country: \"Colombia\", flag: \"https://flagcdn.com/w20/co.png\" },\r\n  { code: \"+51\", country: \"Peru\", flag: \"https://flagcdn.com/w20/pe.png\" },\r\n  { code: \"+58\", country: \"Venezuela\", flag: \"https://flagcdn.com/w20/ve.png\" },\r\n  { code: \"+593\", country: \"Ecuador\", flag: \"https://flagcdn.com/w20/ec.png\" },\r\n  { code: \"+595\", country: \"Paraguay\", flag: \"https://flagcdn.com/w20/py.png\" },\r\n  { code: \"+598\", country: \"Uruguay\", flag: \"https://flagcdn.com/w20/uy.png\" },\r\n];\r\n\r\nconst WhatsAppLinkDialog = ({\r\n  setOpenWhatsAppDialog,\r\n  openWhatsAppDialog,\r\n  Id,\r\n  editingContact = null,\r\n  fetchProfile,\r\n  clearEditingContact,\r\n}) => {\r\n  const [whatsAppNumber, setWhatsAppNumber] = useState(\"\");\r\n  const [contactName, setContactName] = useState(\"\");\r\n  const [selectedCountryCode, setSelectedCountryCode] = useState(\"+1\");\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  // Function to extract country code from a phone number\r\n  const extractCountryCode = (phoneNumber) => {\r\n    if (!phoneNumber || !phoneNumber.startsWith(\"+\")) {\r\n      return \"+1\"; // Default to US\r\n    }\r\n\r\n    // Sort country codes by length (longest first) to match correctly\r\n    const sortedCodes = countryCodes\r\n      .map((c) => c.code)\r\n      .filter((code, index, arr) => arr.indexOf(code) === index) // Remove duplicates\r\n      .sort((a, b) => b.length - a.length);\r\n\r\n    for (const code of sortedCodes) {\r\n      if (phoneNumber.startsWith(code)) {\r\n        return code;\r\n      }\r\n    }\r\n\r\n    return \"+1\"; // Default fallback\r\n  };\r\n\r\n  // Function to extract the number without country code\r\n  const extractNumberWithoutCode = (phoneNumber, countryCode) => {\r\n    if (!phoneNumber || !phoneNumber.startsWith(countryCode)) {\r\n      return \"\";\r\n    }\r\n    return phoneNumber.substring(countryCode.length).replace(/^\\s+/, \"\");\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (editingContact) {\r\n      const fullNumber = editingContact.LinkUrl || \"\";\r\n      const extractedCode = extractCountryCode(fullNumber);\r\n      const numberWithoutCode = extractNumberWithoutCode(\r\n        fullNumber,\r\n        extractedCode\r\n      );\r\n\r\n      setSelectedCountryCode(extractedCode);\r\n      setWhatsAppNumber(numberWithoutCode);\r\n      setContactName(editingContact.Title || \"\");\r\n    } else {\r\n      setSelectedCountryCode(\"+1\");\r\n      setWhatsAppNumber(\"\");\r\n      setContactName(\"\");\r\n    }\r\n  }, [editingContact, openWhatsAppDialog]);\r\n\r\n  const handleContactNameChange = (event) => {\r\n    setContactName(event.target.value);\r\n  };\r\n\r\n  const validateWhatsAppNumber = (countryCode, phoneNumber) => {\r\n    if (!phoneNumber || phoneNumber.trim() === \"\") {\r\n      return { isValid: false, error: \"Phone number is required\" };\r\n    }\r\n\r\n    if (!countryCode) {\r\n      return { isValid: false, error: \"Country code is required\" };\r\n    }\r\n\r\n    // Remove all spaces, dashes, parentheses, and other formatting from phone number\r\n    const cleanNumber = phoneNumber.replace(/[\\s\\-\\(\\)\\.]/g, \"\");\r\n\r\n    // Check if it contains only digits after cleaning\r\n    if (!/^\\d+$/.test(cleanNumber)) {\r\n      return {\r\n        isValid: false,\r\n        error:\r\n          \"Phone number should contain only digits, spaces, dashes, or parentheses\",\r\n      };\r\n    }\r\n\r\n    // Country-specific validation patterns (same as Phone)\r\n    const countryValidation = {\r\n      \"+1\": {\r\n        // US/Canada\r\n        minLength: 10,\r\n        maxLength: 10,\r\n        pattern: /^[2-9]\\d{9}$/,\r\n        errorMsg: \"US/Canada numbers should be 10 digits starting with 2-9\",\r\n      },\r\n      \"+44\": {\r\n        // UK\r\n        minLength: 10,\r\n        maxLength: 11,\r\n        pattern: /^[1-9]\\d{9,10}$/,\r\n        errorMsg: \"UK numbers should be 10-11 digits\",\r\n      },\r\n      \"+49\": {\r\n        // Germany\r\n        minLength: 10,\r\n        maxLength: 12,\r\n        pattern: /^[1-9]\\d{9,11}$/,\r\n        errorMsg: \"German numbers should be 10-12 digits\",\r\n      },\r\n      \"+33\": {\r\n        // France\r\n        minLength: 9,\r\n        maxLength: 9,\r\n        pattern: /^[1-9]\\d{8}$/,\r\n        errorMsg: \"French numbers should be 9 digits starting with 1-9\",\r\n      },\r\n      \"+39\": {\r\n        // Italy\r\n        minLength: 9,\r\n        maxLength: 11,\r\n        pattern: /^[0-9]\\d{8,10}$/,\r\n        errorMsg: \"Italian numbers should be 9-11 digits\",\r\n      },\r\n      \"+34\": {\r\n        // Spain\r\n        minLength: 9,\r\n        maxLength: 9,\r\n        pattern: /^[6-9]\\d{8}$/,\r\n        errorMsg: \"Spanish mobile numbers should be 9 digits starting with 6-9\",\r\n      },\r\n      \"+91\": {\r\n        // India\r\n        minLength: 10,\r\n        maxLength: 10,\r\n        pattern: /^[6-9]\\d{9}$/,\r\n        errorMsg: \"Indian mobile numbers should be 10 digits starting with 6-9\",\r\n      },\r\n      \"+86\": {\r\n        // China\r\n        minLength: 11,\r\n        maxLength: 11,\r\n        pattern: /^1[3-9]\\d{9}$/,\r\n        errorMsg:\r\n          \"Chinese mobile numbers should be 11 digits starting with 13-19\",\r\n      },\r\n      \"+81\": {\r\n        // Japan\r\n        minLength: 10,\r\n        maxLength: 11,\r\n        pattern: /^[7-9]\\d{9,10}$/,\r\n        errorMsg:\r\n          \"Japanese mobile numbers should be 10-11 digits starting with 7-9\",\r\n      },\r\n      \"+82\": {\r\n        // South Korea\r\n        minLength: 9,\r\n        maxLength: 10,\r\n        pattern: /^1[0-9]\\d{7,8}$/,\r\n        errorMsg:\r\n          \"Korean mobile numbers should be 9-10 digits starting with 10-19\",\r\n      },\r\n      \"+971\": {\r\n        // UAE\r\n        minLength: 9,\r\n        maxLength: 9,\r\n        pattern: /^5\\d{8}$/,\r\n        errorMsg: \"UAE mobile numbers should be 9 digits starting with 5\",\r\n      },\r\n      \"+966\": {\r\n        // Saudi Arabia\r\n        minLength: 9,\r\n        maxLength: 9,\r\n        pattern: /^5\\d{8}$/,\r\n        errorMsg: \"Saudi mobile numbers should be 9 digits starting with 5\",\r\n      },\r\n      \"+55\": {\r\n        // Brazil\r\n        minLength: 10,\r\n        maxLength: 11,\r\n        pattern: /^[1-9]\\d{9,10}$/,\r\n        errorMsg: \"Brazilian mobile numbers should be 10-11 digits\",\r\n      },\r\n      \"+52\": {\r\n        // Mexico\r\n        minLength: 10,\r\n        maxLength: 10,\r\n        pattern: /^[1-9]\\d{9}$/,\r\n        errorMsg: \"Mexican mobile numbers should be 10 digits\",\r\n      },\r\n      \"+216\": {\r\n        // Tunisia\r\n        minLength: 8,\r\n        maxLength: 8,\r\n        pattern: /^[0-9]{8}$/,\r\n        errorMsg: \"Tunisian mobile numbers should be exactly 8 digits\",\r\n      },\r\n    };\r\n\r\n    const validation = countryValidation[countryCode];\r\n\r\n    if (validation) {\r\n      // Check length\r\n      if (cleanNumber.length < validation.minLength) {\r\n        return {\r\n          isValid: false,\r\n          error: `Number too short. ${validation.errorMsg}`,\r\n        };\r\n      }\r\n      if (cleanNumber.length > validation.maxLength) {\r\n        return {\r\n          isValid: false,\r\n          error: `Number too long. ${validation.errorMsg}`,\r\n        };\r\n      }\r\n\r\n      // Check pattern\r\n      if (!validation.pattern.test(cleanNumber)) {\r\n        return { isValid: false, error: validation.errorMsg };\r\n      }\r\n    } else {\r\n      // General validation for countries not specifically listed\r\n      if (cleanNumber.length < 7) {\r\n        return {\r\n          isValid: false,\r\n          error: \"Phone number too short (minimum 7 digits)\",\r\n        };\r\n      }\r\n      if (cleanNumber.length > 15) {\r\n        return {\r\n          isValid: false,\r\n          error: \"Phone number too long (maximum 15 digits)\",\r\n        };\r\n      }\r\n    }\r\n\r\n    return { isValid: true, error: null };\r\n  };\r\n\r\n  const isValidWhatsAppNumber = (countryCode, phoneNumber) => {\r\n    const validation = validateWhatsAppNumber(countryCode, phoneNumber);\r\n    return validation.isValid;\r\n  };\r\n\r\n  const getValidationError = (countryCode, phoneNumber) => {\r\n    const validation = validateWhatsAppNumber(countryCode, phoneNumber);\r\n    return validation.error;\r\n  };\r\n\r\n  const handleDone = async () => {\r\n    // Validate using separated country code and phone number\r\n    const validationError = getValidationError(\r\n      selectedCountryCode,\r\n      whatsAppNumber\r\n    );\r\n    if (validationError) {\r\n      toast.error(validationError, {\r\n        position: \"top-center\",\r\n        autoClose: 3000,\r\n      });\r\n      return;\r\n    }\r\n\r\n    // Combine country code with number for storage\r\n    const fullNumber = selectedCountryCode + whatsAppNumber.replace(/^\\s+/, \"\");\r\n\r\n    if (!contactName.trim()) {\r\n      toast.error(\"Contact name is required\", {\r\n        position: \"top-center\",\r\n        autoClose: 2000,\r\n      });\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    let response;\r\n\r\n    try {\r\n      if (editingContact) {\r\n        response = await EditContact({\r\n          Id: editingContact.Id,\r\n          ContactInfo: fullNumber,\r\n          Category: \"WhatsApp\",\r\n          Title: contactName.trim(),\r\n          isPublic: true,\r\n        });\r\n      } else {\r\n        response = await CreateContact({\r\n          UserId: Id,\r\n          ContactInfo: fullNumber,\r\n          Category: \"WhatsApp\",\r\n          Title: contactName.trim(),\r\n          isPublic: true,\r\n        });\r\n      }\r\n\r\n      localStorage.setItem(\"isLinksCardVisible\", \"true\");\r\n\r\n      setContactName(\"\");\r\n      setWhatsAppNumber(\"\");\r\n      setContactName(\"\");\r\n      if (clearEditingContact) clearEditingContact();\r\n      setOpenWhatsAppDialog(false);\r\n\r\n      if (response) {\r\n        toast.success(\r\n          editingContact\r\n            ? \"WhatsApp contact updated\"\r\n            : \"WhatsApp contact added\",\r\n          {\r\n            position: \"top-center\",\r\n            autoClose: 1000,\r\n          }\r\n        );\r\n        if (fetchProfile) fetchProfile();\r\n      } else {\r\n        toast.error(\"Error while saving WhatsApp contact\", {\r\n          position: \"top-center\",\r\n          autoClose: 1000,\r\n        });\r\n      }\r\n    } catch (error) {\r\n      toast.error(\"Error while saving WhatsApp contact\", {\r\n        position: \"top-center\",\r\n        autoClose: 1000,\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog\r\n      open={openWhatsAppDialog}\r\n      onClose={() => {\r\n        setWhatsAppNumber(\"\");\r\n        setContactName(\"\");\r\n        if (clearEditingContact) clearEditingContact();\r\n        setOpenWhatsAppDialog(false);\r\n      }}\r\n    >\r\n      <DialogTitle>\r\n        {editingContact ? \"Edit WhatsApp Contact\" : \"Add WhatsApp Contact\"}\r\n      </DialogTitle>\r\n      <DialogContent>\r\n        <TextField\r\n          name=\"contactName\"\r\n          autoFocus\r\n          margin=\"dense\"\r\n          label=\"Contact Name\"\r\n          type=\"text\"\r\n          fullWidth\r\n          required\r\n          value={contactName}\r\n          onChange={handleContactNameChange}\r\n          helperText={contactName === \"\" ? \"Contact name is required\" : \"\"}\r\n          sx={{ mb: 2 }}\r\n        />\r\n        <Grid container spacing={2}>\r\n          <Grid item xs={12} sm={4}>\r\n            <FormControl fullWidth margin=\"dense\">\r\n              <InputLabel>Country</InputLabel>\r\n              <Select\r\n                value={selectedCountryCode}\r\n                onChange={(e) => setSelectedCountryCode(e.target.value)}\r\n                label=\"Country\"\r\n                sx={{\r\n                  \"& .MuiSelect-select\": {\r\n                    padding: { xs: \"12px 14px\", sm: \"16.5px 14px\" },\r\n                    fontSize: { xs: \"0.875rem\", sm: \"1rem\" },\r\n                  },\r\n                }}\r\n                renderValue={(value) => {\r\n                  const country = countryCodes.find((c) => c.code === value);\r\n                  return country ? (\r\n                    <Box\r\n                      sx={{\r\n                        display: \"flex\",\r\n                        alignItems: \"center\",\r\n                        gap: { xs: 0.5, sm: 1 },\r\n                        minWidth: 0,\r\n                      }}\r\n                    >\r\n                      <img\r\n                        src={country.flag}\r\n                        alt={country.country}\r\n                        style={{\r\n                          width: 20,\r\n                          height: 15,\r\n                          flexShrink: 0,\r\n                        }}\r\n                      />\r\n                      <span\r\n                        style={{\r\n                          fontSize: \"inherit\",\r\n                          overflow: \"hidden\",\r\n                          textOverflow: \"ellipsis\",\r\n                          whiteSpace: \"nowrap\",\r\n                        }}\r\n                      >\r\n                        {value}\r\n                      </span>\r\n                    </Box>\r\n                  ) : (\r\n                    value\r\n                  );\r\n                }}\r\n              >\r\n                {countryCodes.map((country, index) => (\r\n                  <MenuItem\r\n                    key={`${country.code}-${index}`}\r\n                    value={country.code}\r\n                    sx={{\r\n                      padding: { xs: \"8px 16px\", sm: \"6px 16px\" },\r\n                      minHeight: { xs: \"48px\", sm: \"auto\" },\r\n                    }}\r\n                  >\r\n                    <Box\r\n                      sx={{\r\n                        display: \"flex\",\r\n                        alignItems: \"center\",\r\n                        gap: 1,\r\n                        width: \"100%\",\r\n                        minWidth: 0,\r\n                      }}\r\n                    >\r\n                      <img\r\n                        src={country.flag}\r\n                        alt={country.country}\r\n                        style={{\r\n                          width: 20,\r\n                          height: 15,\r\n                          flexShrink: 0,\r\n                        }}\r\n                      />\r\n                      <span\r\n                        style={{\r\n                          fontWeight: 500,\r\n                          flexShrink: 0,\r\n                        }}\r\n                      >\r\n                        {country.code}\r\n                      </span>\r\n                      <span\r\n                        style={{\r\n                          fontSize: \"0.875rem\",\r\n                          color: \"#666\",\r\n                          overflow: \"hidden\",\r\n                          textOverflow: \"ellipsis\",\r\n                          whiteSpace: \"nowrap\",\r\n                          flex: 1,\r\n                          minWidth: 0,\r\n                        }}\r\n                      >\r\n                        {country.country}\r\n                      </span>\r\n                    </Box>\r\n                  </MenuItem>\r\n                ))}\r\n              </Select>\r\n            </FormControl>\r\n          </Grid>\r\n          <Grid item xs={12} sm={8}>\r\n            <TextField\r\n              name=\"WhatsAppNumber\"\r\n              margin=\"dense\"\r\n              label=\"Phone Number\"\r\n              type=\"tel\"\r\n              fullWidth\r\n              required\r\n              value={whatsAppNumber}\r\n              onChange={(e) =>\r\n                setWhatsAppNumber(e.target.value.replace(/[^\\d\\s\\-\\(\\)]/g, \"\"))\r\n              }\r\n              error={\r\n                whatsAppNumber !== \"\" &&\r\n                !isValidWhatsAppNumber(selectedCountryCode, whatsAppNumber)\r\n              }\r\n              helperText={\r\n                whatsAppNumber === \"\"\r\n                  ? \"Phone number is required\"\r\n                  : whatsAppNumber !== \"\" &&\r\n                    !isValidWhatsAppNumber(selectedCountryCode, whatsAppNumber)\r\n                  ? getValidationError(selectedCountryCode, whatsAppNumber)\r\n                  : \"✓ Valid WhatsApp number\"\r\n              }\r\n              placeholder=\"************\"\r\n              inputProps={{\r\n                maxLength: 15,\r\n              }}\r\n              sx={{\r\n                \"& .MuiInputBase-input\": {\r\n                  fontSize: { xs: \"16px\", sm: \"1rem\" }, // Prevents zoom on iOS\r\n                  padding: { xs: \"12px 14px\", sm: \"16.5px 14px\" },\r\n                },\r\n                \"& .MuiFormHelperText-root\": {\r\n                  fontSize: { xs: \"0.75rem\", sm: \"0.75rem\" },\r\n                  marginTop: { xs: \"4px\", sm: \"3px\" },\r\n                },\r\n              }}\r\n            />\r\n          </Grid>\r\n        </Grid>\r\n\r\n        {/* Preview of full number */}\r\n        {whatsAppNumber && (\r\n          <Box\r\n            sx={{\r\n              mt: { xs: 2, sm: 1 },\r\n              mb: { xs: 2, sm: 1 },\r\n              display: \"flex\",\r\n              justifyContent: { xs: \"center\", sm: \"flex-start\" },\r\n            }}\r\n          >\r\n            <Chip\r\n              label={`Full Number: ${selectedCountryCode} ${whatsAppNumber}`}\r\n              variant=\"outlined\"\r\n              size=\"small\"\r\n              color={\r\n                isValidWhatsAppNumber(selectedCountryCode, whatsAppNumber)\r\n                  ? \"success\"\r\n                  : \"default\"\r\n              }\r\n              sx={{\r\n                fontSize: { xs: \"0.75rem\", sm: \"0.8125rem\" },\r\n                height: { xs: \"28px\", sm: \"24px\" },\r\n                \"& .MuiChip-label\": {\r\n                  padding: { xs: \"0 8px\", sm: \"0 12px\" },\r\n                },\r\n              }}\r\n            />\r\n          </Box>\r\n        )}\r\n        {/* Hints and Tips Section */}\r\n        <Box\r\n          mt={2}\r\n          p={2}\r\n          sx={{\r\n            backgroundColor: \"#f0f0f0\",\r\n            borderRadius: \"5px\",\r\n          }}\r\n        >\r\n          <Typography variant=\"subtitle1\" color=\"textPrimary\">\r\n            How to Add WhatsApp Contact\r\n          </Typography>\r\n          <Typography variant=\"body2\" color=\"textSecondary\">\r\n            • <strong>Select your country:</strong> Choose from the dropdown\r\n            list with flags\r\n          </Typography>\r\n          <Typography variant=\"body2\" color=\"textSecondary\">\r\n            • <strong>Enter phone number:</strong> Only digits, minimum 8 digits\r\n            (e.g., 23456789)\r\n          </Typography>\r\n          <Typography variant=\"body2\" color=\"textSecondary\">\r\n            • <strong>No spaces or symbols:</strong> Just the numbers without\r\n            any formatting\r\n          </Typography>\r\n          <Typography variant=\"body2\" color=\"textSecondary\">\r\n            • <strong>Preview:</strong> See the full international number below\r\n          </Typography>\r\n          <Typography variant=\"body2\" color=\"textSecondary\">\r\n            • <strong>Multiple contacts:</strong> Add different WhatsApp numbers\r\n            with unique names\r\n          </Typography>\r\n        </Box>\r\n      </DialogContent>\r\n      <DialogActions>\r\n        <Button\r\n          onClick={() => {\r\n            setContactName(\"\");\r\n            setWhatsAppNumber(\"\");\r\n            setContactName(\"\");\r\n            if (clearEditingContact) clearEditingContact();\r\n            setOpenWhatsAppDialog(false);\r\n          }}\r\n        >\r\n          Cancel\r\n        </Button>\r\n        <Button\r\n          onClick={handleDone}\r\n          disabled={\r\n            whatsAppNumber === \"\" ||\r\n            !isValidWhatsAppNumber(selectedCountryCode, whatsAppNumber) ||\r\n            contactName.trim() === \"\" ||\r\n            isLoading\r\n          }\r\n        >\r\n          {isLoading ? \"Saving...\" : editingContact ? \"Update\" : \"Add\"}\r\n        </Button>\r\n      </DialogActions>\r\n    </Dialog>\r\n  );\r\n};\r\n\r\nexport default WhatsAppLinkDialog;\r\n", "C:\\Users\\<USER>\\Desktop\\iDigitalX\\ClientApp\\src\\sections\\@dashboard\\Link\\EmailLinkDialog.js", [], [], {"ruleId": "739", "severity": 1, "message": "740", "line": 14, "column": 8, "nodeType": "741", "endLine": 14, "endColumn": 17, "suggestions": "742"}, {"ruleId": "743", "severity": 1, "message": "744", "line": 1, "column": 54, "nodeType": "745", "messageId": "746", "endLine": 1, "endColumn": 63}, {"ruleId": "747", "severity": 1, "message": "748", "line": 30, "column": 14, "nodeType": "745", "messageId": "749", "endLine": 30, "endColumn": 24}, {"ruleId": "739", "severity": 1, "message": "750", "line": 34, "column": 6, "nodeType": "741", "endLine": 34, "endColumn": 8, "suggestions": "751"}, {"ruleId": "743", "severity": 1, "message": "752", "line": 33, "column": 7, "nodeType": "745", "messageId": "746", "endLine": 33, "endColumn": 23}, {"ruleId": "743", "severity": 1, "message": "753", "line": 41, "column": 7, "nodeType": "745", "messageId": "746", "endLine": 41, "endColumn": 15}, {"ruleId": "743", "severity": 1, "message": "754", "line": 50, "column": 10, "nodeType": "745", "messageId": "746", "endLine": 50, "endColumn": 24}, {"ruleId": "743", "severity": 1, "message": "755", "line": 53, "column": 10, "nodeType": "745", "messageId": "746", "endLine": 53, "endColumn": 25}, {"ruleId": "756", "severity": 1, "message": "757", "line": 143, "column": 28, "nodeType": "758", "messageId": "759", "endLine": 143, "endColumn": 30}, {"ruleId": "756", "severity": 1, "message": "757", "line": 143, "column": 50, "nodeType": "758", "messageId": "759", "endLine": 143, "endColumn": 52}, {"ruleId": "739", "severity": 1, "message": "760", "line": 33, "column": 6, "nodeType": "741", "endLine": 33, "endColumn": 33, "suggestions": "761"}, {"ruleId": "762", "severity": 1, "message": "763", "line": 46, "column": 41, "nodeType": "764", "messageId": "765", "endLine": 46, "endColumn": 42, "suggestions": "766"}, {"ruleId": "743", "severity": 1, "message": "767", "line": 34, "column": 7, "nodeType": "745", "messageId": "746", "endLine": 34, "endColumn": 18}, {"ruleId": "743", "severity": 1, "message": "768", "line": 53, "column": 7, "nodeType": "745", "messageId": "746", "endLine": 53, "endColumn": 15}, {"ruleId": "743", "severity": 1, "message": "769", "line": 59, "column": 7, "nodeType": "745", "messageId": "746", "endLine": 59, "endColumn": 12}, {"ruleId": "743", "severity": 1, "message": "770", "line": 65, "column": 7, "nodeType": "745", "messageId": "746", "endLine": 65, "endColumn": 16}, {"ruleId": "756", "severity": 1, "message": "771", "line": 161, "column": 37, "nodeType": "758", "messageId": "759", "endLine": 161, "endColumn": 39}, {"ruleId": "756", "severity": 1, "message": "771", "line": 162, "column": 38, "nodeType": "758", "messageId": "759", "endLine": 162, "endColumn": 40}, {"ruleId": "743", "severity": 1, "message": "772", "line": 28, "column": 7, "nodeType": "745", "messageId": "746", "endLine": 28, "endColumn": 22}, {"ruleId": "739", "severity": 1, "message": "773", "line": 195, "column": 6, "nodeType": "741", "endLine": 195, "endColumn": 20, "suggestions": "774"}, {"ruleId": "739", "severity": 1, "message": "775", "line": 203, "column": 6, "nodeType": "741", "endLine": 203, "endColumn": 32, "suggestions": "776"}, {"ruleId": "743", "severity": 1, "message": "777", "line": 212, "column": 10, "nodeType": "745", "messageId": "746", "endLine": 212, "endColumn": 24}, {"ruleId": "762", "severity": 1, "message": "763", "line": 554, "column": 52, "nodeType": "764", "messageId": "765", "endLine": 554, "endColumn": 53, "suggestions": "778"}, {"ruleId": "739", "severity": 1, "message": "779", "line": 677, "column": 6, "nodeType": "741", "endLine": 677, "endColumn": 15, "suggestions": "780"}, {"ruleId": "739", "severity": 1, "message": "781", "line": 712, "column": 6, "nodeType": "741", "endLine": 712, "endColumn": 8, "suggestions": "782"}, {"ruleId": "762", "severity": 1, "message": "763", "line": 872, "column": 27, "nodeType": "783", "messageId": "765", "endLine": 872, "endColumn": 28, "suggestions": "784"}, {"ruleId": "762", "severity": 1, "message": "763", "line": 872, "column": 29, "nodeType": "783", "messageId": "765", "endLine": 872, "endColumn": 30, "suggestions": "785"}, {"ruleId": "762", "severity": 1, "message": "786", "line": 872, "column": 37, "nodeType": "783", "messageId": "765", "endLine": 872, "endColumn": 38, "suggestions": "787"}, {"ruleId": "762", "severity": 1, "message": "763", "line": 876, "column": 16, "nodeType": "783", "messageId": "765", "endLine": 876, "endColumn": 17, "suggestions": "788"}, {"ruleId": "762", "severity": 1, "message": "763", "line": 876, "column": 18, "nodeType": "783", "messageId": "765", "endLine": 876, "endColumn": 19, "suggestions": "789"}, {"ruleId": "762", "severity": 1, "message": "786", "line": 876, "column": 26, "nodeType": "783", "messageId": "765", "endLine": 876, "endColumn": 27, "suggestions": "790"}, {"ruleId": "791", "severity": 1, "message": "792", "line": 2317, "column": 25, "nodeType": "793", "endLine": 2317, "endColumn": 468}, {"ruleId": "794", "severity": 1, "message": "795", "line": 2733, "column": 29, "nodeType": "796", "messageId": "759", "endLine": 2736, "endColumn": 37}, {"ruleId": "743", "severity": 1, "message": "797", "line": 134, "column": 13, "nodeType": "745", "messageId": "746", "endLine": 134, "endColumn": 23}, {"ruleId": "739", "severity": 1, "message": "798", "line": 197, "column": 6, "nodeType": "741", "endLine": 197, "endColumn": 15, "suggestions": "799"}, {"ruleId": "739", "severity": 1, "message": "740", "line": 234, "column": 6, "nodeType": "741", "endLine": 234, "endColumn": 15, "suggestions": "800"}, {"ruleId": "801", "severity": 1, "message": "802", "line": 470, "column": 9, "nodeType": "803", "messageId": "804", "endLine": 502, "endColumn": 10}, {"ruleId": "762", "severity": 1, "message": "805", "line": 540, "column": 27, "nodeType": "764", "messageId": "765", "endLine": 540, "endColumn": 28, "suggestions": "806"}, {"ruleId": "807", "severity": 1, "message": "808", "line": 729, "column": 23, "nodeType": "793", "endLine": 735, "endColumn": 24}, {"ruleId": "807", "severity": 1, "message": "808", "line": 751, "column": 25, "nodeType": "793", "endLine": 757, "endColumn": 26}, {"ruleId": "743", "severity": 1, "message": "809", "line": 43, "column": 7, "nodeType": "745", "messageId": "746", "endLine": 43, "endColumn": 17}, {"ruleId": "743", "severity": 1, "message": "810", "line": 62, "column": 7, "nodeType": "745", "messageId": "746", "endLine": 62, "endColumn": 29}, {"ruleId": "743", "severity": 1, "message": "811", "line": 166, "column": 20, "nodeType": "745", "messageId": "746", "endLine": 166, "endColumn": 32}, {"ruleId": "743", "severity": 1, "message": "812", "line": 199, "column": 10, "nodeType": "745", "messageId": "746", "endLine": 199, "endColumn": 29}, {"ruleId": "743", "severity": 1, "message": "813", "line": 200, "column": 10, "nodeType": "745", "messageId": "746", "endLine": 200, "endColumn": 25}, {"ruleId": "743", "severity": 1, "message": "814", "line": 212, "column": 10, "nodeType": "745", "messageId": "746", "endLine": 212, "endColumn": 21}, {"ruleId": "743", "severity": 1, "message": "815", "line": 212, "column": 23, "nodeType": "745", "messageId": "746", "endLine": 212, "endColumn": 37}, {"ruleId": "743", "severity": 1, "message": "816", "line": 213, "column": 10, "nodeType": "745", "messageId": "746", "endLine": 213, "endColumn": 14}, {"ruleId": "743", "severity": 1, "message": "817", "line": 213, "column": 16, "nodeType": "745", "messageId": "746", "endLine": 213, "endColumn": 23}, {"ruleId": "739", "severity": 1, "message": "779", "line": 240, "column": 6, "nodeType": "741", "endLine": 240, "endColumn": 15, "suggestions": "818"}, {"ruleId": "743", "severity": 1, "message": "819", "line": 72, "column": 10, "nodeType": "745", "messageId": "746", "endLine": 72, "endColumn": 29}, {"ruleId": "820", "severity": 1, "message": "821", "line": 122, "column": 11, "nodeType": "745", "messageId": "746", "endLine": 122, "endColumn": 19}, {"ruleId": "743", "severity": 1, "message": "822", "line": 50, "column": 7, "nodeType": "745", "messageId": "746", "endLine": 50, "endColumn": 13}, {"ruleId": "743", "severity": 1, "message": "823", "line": 120, "column": 10, "nodeType": "745", "messageId": "746", "endLine": 120, "endColumn": 22}, {"ruleId": "756", "severity": 1, "message": "757", "line": 131, "column": 34, "nodeType": "758", "messageId": "759", "endLine": 131, "endColumn": 36}, {"ruleId": "739", "severity": 1, "message": "824", "line": 137, "column": 6, "nodeType": "741", "endLine": 137, "endColumn": 21, "suggestions": "825"}, {"ruleId": "743", "severity": 1, "message": "826", "line": 146, "column": 9, "nodeType": "745", "messageId": "746", "endLine": 146, "endColumn": 33}, {"ruleId": "743", "severity": 1, "message": "827", "line": 159, "column": 9, "nodeType": "745", "messageId": "746", "endLine": 159, "endColumn": 24}, {"ruleId": "762", "severity": 1, "message": "828", "line": 178, "column": 46, "nodeType": "764", "messageId": "765", "endLine": 178, "endColumn": 47, "suggestions": "829"}, {"ruleId": "756", "severity": 1, "message": "757", "line": 838, "column": 22, "nodeType": "758", "messageId": "759", "endLine": 838, "endColumn": 24}, {"ruleId": "791", "severity": 1, "message": "792", "line": 859, "column": 15, "nodeType": "793", "endLine": 859, "endColumn": 132}, {"ruleId": "830", "severity": 2, "message": "831", "line": 43, "column": 23, "nodeType": "745", "endLine": 43, "endColumn": 36, "suppressions": "832"}, {"ruleId": "801", "severity": 1, "message": "802", "line": 75, "column": 9, "nodeType": "803", "messageId": "804", "endLine": 91, "endColumn": 10}, {"ruleId": "756", "severity": 1, "message": "757", "line": 27, "column": 20, "nodeType": "758", "messageId": "759", "endLine": 27, "endColumn": 22}, {"ruleId": "756", "severity": 1, "message": "757", "line": 32, "column": 16, "nodeType": "758", "messageId": "759", "endLine": 32, "endColumn": 18}, {"ruleId": "807", "severity": 1, "message": "833", "line": 97, "column": 15, "nodeType": "793", "endLine": 97, "endColumn": 51}, {"ruleId": "807", "severity": 1, "message": "833", "line": 109, "column": 15, "nodeType": "793", "endLine": 109, "endColumn": 52}, {"ruleId": "807", "severity": 1, "message": "833", "line": 239, "column": 15, "nodeType": "793", "endLine": 239, "endColumn": 51}, {"ruleId": "807", "severity": 1, "message": "833", "line": 251, "column": 15, "nodeType": "793", "endLine": 251, "endColumn": 52}, {"ruleId": "743", "severity": 1, "message": "834", "line": 6, "column": 10, "nodeType": "745", "messageId": "746", "endLine": 6, "endColumn": 24}, {"ruleId": "743", "severity": 1, "message": "835", "line": 10, "column": 5, "nodeType": "745", "messageId": "746", "endLine": 10, "endColumn": 9}, {"ruleId": "743", "severity": 1, "message": "836", "line": 280, "column": 11, "nodeType": "745", "messageId": "746", "endLine": 280, "endColumn": 16}, {"ruleId": "743", "severity": 1, "message": "837", "line": 27, "column": 7, "nodeType": "745", "messageId": "746", "endLine": 27, "endColumn": 22}, {"ruleId": "743", "severity": 1, "message": "838", "line": 36, "column": 7, "nodeType": "745", "messageId": "746", "endLine": 36, "endColumn": 22}, {"ruleId": "743", "severity": 1, "message": "839", "line": 48, "column": 7, "nodeType": "745", "messageId": "746", "endLine": 48, "endColumn": 22}, {"ruleId": "743", "severity": 1, "message": "840", "line": 69, "column": 7, "nodeType": "745", "messageId": "746", "endLine": 69, "endColumn": 20}, {"ruleId": "762", "severity": 1, "message": "841", "line": 200, "column": 51, "nodeType": "764", "messageId": "765", "endLine": 200, "endColumn": 52, "suggestions": "842"}, {"ruleId": "762", "severity": 1, "message": "843", "line": 200, "column": 53, "nodeType": "764", "messageId": "765", "endLine": 200, "endColumn": 54, "suggestions": "844"}, {"ruleId": "762", "severity": 1, "message": "786", "line": 200, "column": 55, "nodeType": "764", "messageId": "765", "endLine": 200, "endColumn": 56, "suggestions": "845"}, {"ruleId": "762", "severity": 1, "message": "841", "line": 594, "column": 64, "nodeType": "764", "messageId": "765", "endLine": 594, "endColumn": 65, "suggestions": "846"}, {"ruleId": "762", "severity": 1, "message": "843", "line": 594, "column": 66, "nodeType": "764", "messageId": "765", "endLine": 594, "endColumn": 67, "suggestions": "847"}, {"ruleId": "756", "severity": 1, "message": "771", "line": 101, "column": 21, "nodeType": "758", "messageId": "759", "endLine": 101, "endColumn": 23}, {"ruleId": "743", "severity": 1, "message": "848", "line": 27, "column": 10, "nodeType": "745", "messageId": "746", "endLine": 27, "endColumn": 23}, {"ruleId": "743", "severity": 1, "message": "849", "line": 136, "column": 9, "nodeType": "745", "messageId": "746", "endLine": 136, "endColumn": 25}, {"ruleId": "743", "severity": 1, "message": "850", "line": 140, "column": 9, "nodeType": "745", "messageId": "746", "endLine": 140, "endColumn": 26}, {"ruleId": "791", "severity": 1, "message": "792", "line": 161, "column": 11, "nodeType": "793", "endLine": 168, "endColumn": 13}, {"ruleId": "739", "severity": 1, "message": "824", "line": 72, "column": 6, "nodeType": "741", "endLine": 72, "endColumn": 37, "suggestions": "851"}, {"ruleId": "739", "severity": 1, "message": "824", "line": 21, "column": 8, "nodeType": "741", "endLine": 21, "endColumn": 18, "suggestions": "852"}, {"ruleId": "739", "severity": 1, "message": "853", "line": 6, "column": 8, "nodeType": "741", "endLine": 6, "endColumn": 10, "suggestions": "854"}, {"ruleId": "743", "severity": 1, "message": "855", "line": 23, "column": 29, "nodeType": "745", "messageId": "746", "endLine": 23, "endColumn": 47}, {"ruleId": "739", "severity": 1, "message": "824", "line": 42, "column": 8, "nodeType": "741", "endLine": 42, "endColumn": 18, "suggestions": "856"}, {"ruleId": "743", "severity": 1, "message": "857", "line": 37, "column": 11, "nodeType": "745", "messageId": "746", "endLine": 37, "endColumn": 25}, {"ruleId": "791", "severity": 1, "message": "792", "line": 61, "column": 29, "nodeType": "793", "endLine": 68, "endColumn": 31}, {"ruleId": "756", "severity": 1, "message": "757", "line": 51, "column": 13, "nodeType": "758", "messageId": "759", "endLine": 51, "endColumn": 15}, {"ruleId": "743", "severity": 1, "message": "858", "line": 6, "column": 7, "nodeType": "745", "messageId": "746", "endLine": 6, "endColumn": 18}, {"ruleId": "743", "severity": 1, "message": "744", "line": 1, "column": 27, "nodeType": "745", "messageId": "746", "endLine": 1, "endColumn": 36}, {"ruleId": "762", "severity": 1, "message": "828", "line": 80, "column": 30, "nodeType": "764", "messageId": "765", "endLine": 80, "endColumn": 31, "suggestions": "859"}, {"ruleId": "762", "severity": 1, "message": "763", "line": 80, "column": 47, "nodeType": "764", "messageId": "765", "endLine": 80, "endColumn": 48, "suggestions": "860"}, {"ruleId": "739", "severity": 1, "message": "798", "line": 52, "column": 6, "nodeType": "741", "endLine": 52, "endColumn": 15, "suggestions": "861"}, {"ruleId": "743", "severity": 1, "message": "862", "line": 11, "column": 5, "nodeType": "745", "messageId": "746", "endLine": 11, "endColumn": 9}, {"ruleId": "743", "severity": 1, "message": "863", "line": 10, "column": 7, "nodeType": "745", "messageId": "746", "endLine": 10, "endColumn": 12}, {"ruleId": "762", "severity": 1, "message": "841", "line": 201, "column": 51, "nodeType": "764", "messageId": "765", "endLine": 201, "endColumn": 52, "suggestions": "864"}, {"ruleId": "762", "severity": 1, "message": "843", "line": 201, "column": 53, "nodeType": "764", "messageId": "765", "endLine": 201, "endColumn": 54, "suggestions": "865"}, {"ruleId": "762", "severity": 1, "message": "786", "line": 201, "column": 55, "nodeType": "764", "messageId": "765", "endLine": 201, "endColumn": 56, "suggestions": "866"}, {"ruleId": "762", "severity": 1, "message": "841", "line": 597, "column": 67, "nodeType": "764", "messageId": "765", "endLine": 597, "endColumn": 68, "suggestions": "867"}, {"ruleId": "762", "severity": 1, "message": "843", "line": 597, "column": 69, "nodeType": "764", "messageId": "765", "endLine": 597, "endColumn": 70, "suggestions": "868"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchProfile'. Either include it or remove the dependency array.", "ArrayExpression", ["869"], "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "@typescript-eslint/no-redeclare", "'PostSearch' is already defined.", "redeclared", "React Hook useEffect has a missing dependency: 'verify'. Either include it or remove the dependency array.", ["870"], "'AppWebsiteVisits' is assigned a value but never used.", "'AppTasks' is assigned a value but never used.", "'CategoryClicks' is assigned a value but never used.", "'LinksTotalCount' is assigned a value but never used.", "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "React Hook useEffect has a missing dependency: 'validatePassword'. Either include it or remove the dependency array.", ["871"], "no-useless-escape", "Unnecessary escape character: \\/.", "Literal", "unnecessaryEscape", ["872", "873"], "'reservdlist' is assigned a value but never used.", "'invoices' is assigned a value but never used.", "'fDate' is assigned a value but never used.", "'fCurrency' is assigned a value but never used.", "Expected '!==' and instead saw '!='.", "'EmojiPeopleIcon' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchSearchResults'. Either include it or remove the dependency array.", ["874"], "React Hook useEffect has a missing dependency: 'sortResultsByTab'. Either include it or remove the dependency array.", ["875"], "'CategoryChosen' is assigned a value but never used.", ["876", "877"], "React Hook useEffect has a missing dependency: 'fetchUserData'. Either include it or remove the dependency array.", ["878"], "React Hook useEffect has missing dependencies: 'fetchUserData' and 'profile'. Either include them or remove the dependency array.", ["879"], "TemplateElement", ["880", "881"], ["882", "883"], "Unnecessary escape character: \\..", ["884", "885"], ["886", "887"], ["888", "889"], ["890", "891"], "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", "no-duplicate-case", "Duplicate case label.", "SwitchCase", "'sessionKey' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchProfileData'. Either include it or remove the dependency array.", ["892"], ["893"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "Unnecessary escape character: \\-.", ["894", "895"], "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'DeleteIcon' is assigned a value but never used.", "'BookingCustomerReviews' is assigned a value but never used.", "'fetchProfile' is assigned a value but never used.", "'showReservedCoupons' is assigned a value but never used.", "'showUsedCoupons' is assigned a value but never used.", "'rowsPerPage' is assigned a value but never used.", "'setRowsPerPage' is assigned a value but never used.", "'page' is assigned a value but never used.", "'setPage' is assigned a value but never used.", ["896"], "'CustomerUsedCoupons' is assigned a value but never used.", "@typescript-eslint/no-unused-vars", "'response' is assigned a value but never used.", "'themes' is assigned a value but never used.", "'hideBranding' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'validateForm'. Either include it or remove the dependency array.", ["897"], "'handleHideBrandingChange' is assigned a value but never used.", "'handleCardClick' is assigned a value but never used.", "Unnecessary escape character: \\[.", ["898", "899"], "react-hooks/rules-of-hooks", "React Hook \"useMediaQuery\" cannot be called inside a callback. React Hooks must be called in a React function component or a custom React Hook function.", ["900"], "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'fShortenNumber' is defined but never used.", "'Chip' is defined but never used.", "'theme' is assigned a value but never used.", "'fadeInAnimation' is assigned a value but never used.", "'bounceAnimation' is assigned a value but never used.", "'rotateAnimation' is assigned a value but never used.", "'glowAnimation' is assigned a value but never used.", "Unnecessary escape character: \\(.", ["901", "902"], "Unnecessary escape character: \\).", ["903", "904"], ["905", "906"], ["907", "908"], ["909", "910"], "'editedContact' is assigned a value but never used.", "'handleDialogOpen' is assigned a value but never used.", "'handleDialogClose' is assigned a value but never used.", ["911"], ["912"], "React Hook useEffect has a missing dependency: 'onSubmit'. Either include it or remove the dependency array. If 'onSubmit' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["913"], "'setUsernameChanged' is assigned a value but never used.", ["914"], "'handleDownload' is assigned a value but never used.", "'StyledCover' is assigned a value but never used.", ["915", "916"], ["917", "918"], ["919"], "'Link' is defined but never used.", "'LANGS' is assigned a value but never used.", ["920", "921"], ["922", "923"], ["924", "925"], ["926", "927"], ["928", "929"], {"desc": "930", "fix": "931"}, {"desc": "932", "fix": "933"}, {"desc": "934", "fix": "935"}, {"messageId": "936", "fix": "937", "desc": "938"}, {"messageId": "939", "fix": "940", "desc": "941"}, {"desc": "942", "fix": "943"}, {"desc": "944", "fix": "945"}, {"messageId": "936", "fix": "946", "desc": "938"}, {"messageId": "939", "fix": "947", "desc": "941"}, {"desc": "948", "fix": "949"}, {"desc": "948", "fix": "950"}, {"messageId": "936", "fix": "951", "desc": "938"}, {"messageId": "939", "fix": "952", "desc": "941"}, {"messageId": "936", "fix": "953", "desc": "938"}, {"messageId": "939", "fix": "954", "desc": "941"}, {"messageId": "936", "fix": "955", "desc": "938"}, {"messageId": "939", "fix": "956", "desc": "941"}, {"messageId": "936", "fix": "957", "desc": "938"}, {"messageId": "939", "fix": "958", "desc": "941"}, {"messageId": "936", "fix": "959", "desc": "938"}, {"messageId": "939", "fix": "960", "desc": "941"}, {"messageId": "936", "fix": "961", "desc": "938"}, {"messageId": "939", "fix": "962", "desc": "941"}, {"desc": "963", "fix": "964"}, {"desc": "930", "fix": "965"}, {"messageId": "936", "fix": "966", "desc": "938"}, {"messageId": "939", "fix": "967", "desc": "941"}, {"desc": "948", "fix": "968"}, {"desc": "969", "fix": "970"}, {"messageId": "936", "fix": "971", "desc": "938"}, {"messageId": "939", "fix": "972", "desc": "941"}, {"kind": "973", "justification": "974"}, {"messageId": "936", "fix": "975", "desc": "938"}, {"messageId": "939", "fix": "976", "desc": "941"}, {"messageId": "936", "fix": "977", "desc": "938"}, {"messageId": "939", "fix": "978", "desc": "941"}, {"messageId": "936", "fix": "979", "desc": "938"}, {"messageId": "939", "fix": "980", "desc": "941"}, {"messageId": "936", "fix": "981", "desc": "938"}, {"messageId": "939", "fix": "982", "desc": "941"}, {"messageId": "936", "fix": "983", "desc": "938"}, {"messageId": "939", "fix": "984", "desc": "941"}, {"desc": "985", "fix": "986"}, {"desc": "987", "fix": "988"}, {"desc": "989", "fix": "990"}, {"desc": "987", "fix": "991"}, {"messageId": "936", "fix": "992", "desc": "938"}, {"messageId": "939", "fix": "993", "desc": "941"}, {"messageId": "936", "fix": "994", "desc": "938"}, {"messageId": "939", "fix": "995", "desc": "941"}, {"desc": "963", "fix": "996"}, {"messageId": "936", "fix": "997", "desc": "938"}, {"messageId": "939", "fix": "998", "desc": "941"}, {"messageId": "936", "fix": "999", "desc": "938"}, {"messageId": "939", "fix": "1000", "desc": "941"}, {"messageId": "936", "fix": "1001", "desc": "938"}, {"messageId": "939", "fix": "1002", "desc": "941"}, {"messageId": "936", "fix": "1003", "desc": "938"}, {"messageId": "939", "fix": "1004", "desc": "941"}, {"messageId": "936", "fix": "1005", "desc": "938"}, {"messageId": "939", "fix": "1006", "desc": "941"}, "Update the dependencies array to be: [fetchProfile, profile]", {"range": "1007", "text": "1008"}, "Update the dependencies array to be: [verify]", {"range": "1009", "text": "1010"}, "Update the dependencies array to be: [password, confirmPassword, validatePassword]", {"range": "1011", "text": "1012"}, "removeEscape", {"range": "1013", "text": "974"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1014", "text": "1015"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [fetchSearchResults, searchParams]", {"range": "1016", "text": "1017"}, "Update the dependencies array to be: [searchResults, activeTab, sortResultsByTab]", {"range": "1018", "text": "1019"}, {"range": "1020", "text": "974"}, {"range": "1021", "text": "1015"}, "Update the dependencies array to be: [fetchUserData, profile]", {"range": "1022", "text": "1023"}, {"range": "1024", "text": "1023"}, {"range": "1025", "text": "974"}, {"range": "1026", "text": "1015"}, {"range": "1027", "text": "974"}, {"range": "1028", "text": "1015"}, {"range": "1029", "text": "974"}, {"range": "1030", "text": "1015"}, {"range": "1031", "text": "974"}, {"range": "1032", "text": "1015"}, {"range": "1033", "text": "974"}, {"range": "1034", "text": "1015"}, {"range": "1035", "text": "974"}, {"range": "1036", "text": "1015"}, "Update the dependencies array to be: [fetchProfileData, profile]", {"range": "1037", "text": "1038"}, {"range": "1039", "text": "1008"}, {"range": "1040", "text": "974"}, {"range": "1041", "text": "1015"}, {"range": "1042", "text": "1023"}, "Update the dependencies array to be: [Profile, User, validateForm]", {"range": "1043", "text": "1044"}, {"range": "1045", "text": "974"}, {"range": "1046", "text": "1015"}, "directive", "", {"range": "1047", "text": "974"}, {"range": "1048", "text": "1015"}, {"range": "1049", "text": "974"}, {"range": "1050", "text": "1015"}, {"range": "1051", "text": "974"}, {"range": "1052", "text": "1015"}, {"range": "1053", "text": "974"}, {"range": "1054", "text": "1015"}, {"range": "1055", "text": "974"}, {"range": "1056", "text": "1015"}, "Update the dependencies array to be: [registrationSuccess, formData, validateForm]", {"range": "1057", "text": "1058"}, "Update the dependencies array to be: [formData, validateForm]", {"range": "1059", "text": "1060"}, "Update the dependencies array to be: [onSubmit]", {"range": "1061", "text": "1062"}, {"range": "1063", "text": "1060"}, {"range": "1064", "text": "974"}, {"range": "1065", "text": "1015"}, {"range": "1066", "text": "974"}, {"range": "1067", "text": "1015"}, {"range": "1068", "text": "1038"}, {"range": "1069", "text": "974"}, {"range": "1070", "text": "1015"}, {"range": "1071", "text": "974"}, {"range": "1072", "text": "1015"}, {"range": "1073", "text": "974"}, {"range": "1074", "text": "1015"}, {"range": "1075", "text": "974"}, {"range": "1076", "text": "1015"}, {"range": "1077", "text": "974"}, {"range": "1078", "text": "1015"}, [428, 437], "[fetchProfile, profile]", [934, 936], "[verify]", [1046, 1073], "[password, confirmPassword, validatePassword]", [1471, 1472], [1471, 1471], "\\", [6208, 6222], "[fetchSearchResults, searchParams]", [6484, 6510], "[searchResults, activeTab, sortResultsByTab]", [15939, 15940], [15939, 15939], [19211, 19220], "[fetchUserData, profile]", [20079, 20081], [24549, 24550], [24549, 24549], [24551, 24552], [24551, 24551], [24559, 24560], [24559, 24559], [24658, 24659], [24658, 24658], [24660, 24661], [24660, 24660], [24668, 24669], [24668, 24668], [6105, 6114], "[fetchProfileData, profile]", [7291, 7300], [16999, 17000], [16999, 16999], [7110, 7119], [4032, 4047], "[Profile, User, validateForm]", [5222, 5223], [5222, 5222], [7884, 7885], [7884, 7884], [7886, 7887], [7886, 7886], [7888, 7889], [7888, 7888], [19817, 19818], [19817, 19817], [19819, 19820], [19819, 19819], [2086, 2117], "[registrationSuccess, formData, validateForm]", [653, 663], "[formData, validateForm]", [141, 143], "[onSubmit]", [1108, 1118], [2439, 2440], [2439, 2439], [2456, 2457], [2456, 2456], [1197, 1206], [7863, 7864], [7863, 7863], [7865, 7866], [7865, 7865], [7867, 7868], [7867, 7867], [19870, 19871], [19870, 19870], [19872, 19873], [19872, 19872]]