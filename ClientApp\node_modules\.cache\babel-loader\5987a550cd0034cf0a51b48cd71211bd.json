{"ast": null, "code": "import api from \"./Api\";\nimport { BASE_URL } from \"./Context/config\";\nexport const GetClicks = async () => {\n  try {\n    const authToken = getAuthToken();\n    const response = await api.get(`${BASE_URL}/Analytics/GetClicks`, {\n      headers: {\n        Authorization: `Bearer ${authToken}`,\n        withCredentials: true\n      }\n    });\n    return response;\n  } catch (error) {\n    console.error(\"Error fetching clicks:\", error);\n    if (error.response) {\n      switch (error.response.status) {\n        case 401:\n          throw new Error(\"Authentication failed. Please log in again.\");\n        case 500:\n          throw new Error(\"Server error while fetching analytics. Please try again later.\");\n        default:\n          throw new Error(\"Failed to fetch click analytics. Please try again.\");\n      }\n    }\n    throw new Error(\"Network error. Please check your internet connection and try again.\");\n  }\n};\n_c = GetClicks;\nexport const GetViews = async () => {\n  try {\n    const authToken = getAuthToken();\n    const response = await api.get(`${BASE_URL}/Analytics/GetViews`, {\n      headers: {\n        Authorization: `Bearer ${authToken}`,\n        withCredentials: true\n      }\n    });\n    return response;\n  } catch (error) {\n    console.error(\"Error fetching views:\", error);\n    if (error.response) {\n      switch (error.response.status) {\n        case 401:\n          throw new Error(\"Authentication failed. Please log in again.\");\n        case 500:\n          throw new Error(\"Server error while fetching view analytics. Please try again later.\");\n        default:\n          throw new Error(\"Failed to fetch view analytics. Please try again.\");\n      }\n    }\n    throw new Error(\"Network error. Please check your internet connection and try again.\");\n  }\n};\n_c2 = GetViews;\nexport const PostClick = async click => {\n  const Data = {\n    userId: click.userId,\n    linkId: click.linkId,\n    gender: click.gender,\n    country: click.country,\n    category: click.category,\n    date: click.date\n  };\n  try {\n    await api.post(`${BASE_URL}/Analytics/CreateClick`, Data, {\n      headers: {\n        withCredentials: true\n      }\n    });\n  } catch (error) {\n    console.error(\"Error posting click:\", error);\n    // Don't throw error for analytics - just log it to avoid disrupting user experience\n    if (error.response && error.response.status !== 500) {\n      console.warn(\"Click tracking failed:\", error.response.status, error.response.data);\n    }\n  }\n};\n_c3 = PostClick;\nexport const PostView = async view => {\n  const Data = {\n    userId: view.userId,\n    gender: view.gender,\n    country: view.country,\n    date: view.date\n  };\n  try {\n    const response = await api.post(`${BASE_URL}/Analytics/CreateView`, Data, {\n      headers: {\n        withCredentials: true\n      }\n    });\n  } catch (error) {\n    console.error(\"Error posting view:\", error);\n    // Don't throw error for analytics - just log it to avoid disrupting user experience\n    if (error.response && error.response.status !== 500) {\n      console.warn(\"View tracking failed:\", error.response.status, error.response.data);\n    }\n  }\n};\n_c4 = PostView;\nfunction getAuthToken() {\n  const cookies = document.cookie.split(\";\");\n  for (let i = 0; i < cookies.length; i++) {\n    const cookie = cookies[i].trim();\n    if (cookie.startsWith(\"authToken=\")) {\n      return cookie.substring(\"authToken=\".length, cookie.length);\n    }\n  }\n  return null;\n}\nexport const calculateDailyViews = data => {\n  const dailyViews = {};\n  if (data[0].views && data[0].views.length > 0) {\n    data[0].views.forEach(view => {\n      const date = new Date(view.date);\n      const formattedDate = `${date.getDate().toString().padStart(2, \"0\")}/` + `${(date.getMonth() + 1).toString().padStart(2, \"0\")}/` + `${date.getFullYear()}`;\n      if (dailyViews[formattedDate]) {\n        dailyViews[formattedDate] += 1;\n      } else {\n        dailyViews[formattedDate] = 1;\n      }\n    });\n  }\n  const result = Object.keys(dailyViews).map(date => ({\n    date,\n    totalClicks: dailyViews[date]\n  }));\n  return result;\n};\nexport const mostViewedCountry = data => {\n  const countryViews = {};\n  if (data && data.length > 0 && data[0].views && data[0].views.length > 0) {\n    data[0].views.forEach(countryView => {\n      const country = countryView.country.trim(); // Remove leading/trailing whitespace\n\n      if (countryViews[country]) {\n        countryViews[country] += 1; // Count occurrences\n      } else {\n        countryViews[country] = 1;\n      }\n    });\n  }\n  let mostViewedCountry = null;\n  let highestViewCount = 0;\n  for (const country in countryViews) {\n    if (countryViews[country] > highestViewCount) {\n      highestViewCount = countryViews[country];\n      mostViewedCountry = country;\n    }\n  }\n  return mostViewedCountry;\n};\nexport const calculateDailyClicks = data => {\n  const dailyClicks = {};\n  data.forEach(item => {\n    if (item.linkClicks && item.linkClicks.length > 0) {\n      item.linkClicks.forEach(click => {\n        const date = new Date(click.date);\n        const formattedDate = `${date.getDate().toString().padStart(2, \"0\")}/` + `${(date.getMonth() + 1).toString().padStart(2, \"0\")}/` + `${date.getFullYear()}`;\n        if (dailyClicks[formattedDate]) {\n          dailyClicks[formattedDate] += 1;\n        } else {\n          dailyClicks[formattedDate] = 1;\n        }\n      });\n    }\n  });\n  const result = Object.keys(dailyClicks).map(date => ({\n    date,\n    totalClicks: dailyClicks[date]\n  }));\n  return result;\n};\nexport const calculateCountryClicks = data => {\n  const countryClicks = {};\n  data.forEach(item => {\n    if (item.linkClicks && item.linkClicks.length > 0) {\n      item.linkClicks.forEach(click => {\n        if (countryClicks[click.country]) {\n          countryClicks[click.country] += 1;\n        } else {\n          countryClicks[click.country] = 1;\n        }\n      });\n    }\n  });\n  const result = Object.keys(countryClicks).map(country => ({\n    country,\n    totalClicks: countryClicks[country]\n  }));\n  return result;\n};\nexport const calculateGenderClicks = data => {\n  const genderClicks = {};\n  data.forEach(item => {\n    if (item.linkClicks && item.linkClicks.length > 0) {\n      item.linkClicks.forEach(click => {\n        if (genderClicks[click.gender]) {\n          genderClicks[click.gender] += 1;\n        } else {\n          genderClicks[click.gender] = 1;\n        }\n      });\n    }\n  });\n  const result = Object.keys(genderClicks).map(gender => ({\n    gender,\n    totalClicks: genderClicks[gender]\n  }));\n  return result;\n};\nexport const MostClickedCountry = data => {\n  const countryClicks = {};\n  data.forEach(item => {\n    if (item.countryClickCounts && item.countryClickCounts.length > 0) {\n      item.countryClickCounts.forEach(countryClick => {\n        const country = countryClick.country;\n        if (countryClicks[country]) {\n          countryClicks[country] += countryClick.clickCount;\n        } else {\n          countryClicks[country] = countryClick.clickCount;\n        }\n      });\n    }\n  });\n  let mostClickedCountry = null;\n  let highestClickCount = 0;\n  for (const country in countryClicks) {\n    if (countryClicks[country] > highestClickCount) {\n      highestClickCount = countryClicks[country];\n      mostClickedCountry = country;\n    }\n  }\n  return mostClickedCountry;\n};\n_c5 = MostClickedCountry;\nexport const getClickCountsByCategory = data => {\n  const categories = [\"Custom\", \"Twitter\", \"GitHub\", \"Instagram\", \"Facebook\", \"LinkedIn\"];\n  const clickCountsByCategory = [];\n  categories.forEach(category => {\n    const totalClickCount = data.reduce((total, item) => {\n      if (item.linkCategory === category) {\n        return total + item.clickCount;\n      }\n      return total;\n    }, 0);\n    clickCountsByCategory.push(totalClickCount);\n  });\n  return clickCountsByCategory;\n};\nexport const GetCategoryClickCounts = data => {\n  const categoryClickCounts = {};\n  data.forEach(item => {\n    if (!categoryClickCounts[item.linkCategory]) categoryClickCounts[item.linkCategory] = 0;\n    categoryClickCounts[item.linkCategory] += item.clickCount;\n  });\n\n  // Convert the accumulated click counts into an array of objects\n  const result = Object.keys(categoryClickCounts).map(category => {\n    var icon = category;\n    var color;\n    switch (category) {\n      case \"Twitter\":\n        icon = \"Twitter-circle\";\n        color = \"#212121\";\n        break;\n      case \"Custom\":\n        icon = \"Profile\";\n        color = \"#007BFF\";\n        break;\n      case \"Facebook\":\n        color = \"#5892d0\";\n        break;\n      case \"Youtube\":\n        color = \"#FF0000\";\n        break;\n      case \"Instagram\":\n        color = \"#D81B60\";\n        break;\n      case \"Github\":\n        color = \"#212121\";\n        break;\n      case \"LinkedIn\":\n        color = \"#00b9f1\";\n        break;\n      default:\n        color = \"#000000\";\n        break;\n    }\n    return {\n      name: category,\n      value: categoryClickCounts[category],\n      icon: icon.toLowerCase(),\n      color: color\n    };\n  });\n  return result;\n};\n_c6 = GetCategoryClickCounts;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"GetClicks\");\n$RefreshReg$(_c2, \"GetViews\");\n$RefreshReg$(_c3, \"PostClick\");\n$RefreshReg$(_c4, \"PostView\");\n$RefreshReg$(_c5, \"MostClickedCountry\");\n$RefreshReg$(_c6, \"GetCategoryClickCounts\");", "map": {"version": 3, "names": ["api", "BASE_URL", "GetClicks", "authToken", "getAuthToken", "response", "get", "headers", "Authorization", "withCredentials", "error", "console", "status", "Error", "_c", "GetViews", "_c2", "PostClick", "click", "Data", "userId", "linkId", "gender", "country", "category", "date", "post", "warn", "data", "_c3", "PostView", "view", "_c4", "cookies", "document", "cookie", "split", "i", "length", "trim", "startsWith", "substring", "calculateDailyViews", "dailyViews", "views", "for<PERSON>ach", "Date", "formattedDate", "getDate", "toString", "padStart", "getMonth", "getFullYear", "result", "Object", "keys", "map", "totalClicks", "mostViewedCountry", "countryViews", "countryView", "highestViewCount", "calculateDailyClicks", "dailyClicks", "item", "linkClicks", "calculateCountryClicks", "countryClicks", "calculateGenderClicks", "genderClicks", "MostClickedCountry", "countryClickCounts", "countryClick", "clickCount", "mostClickedCountry", "highestClickCount", "_c5", "getClickCountsByCategory", "categories", "clickCountsByCategory", "totalClickCount", "reduce", "total", "linkCategory", "push", "GetCategoryClickCounts", "categoryClickCounts", "icon", "color", "name", "value", "toLowerCase", "_c6", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/iDigitalX/ClientApp/src/AnalyticsData.ts"], "sourcesContent": ["import api from \"./Api\";\r\nimport { BASE_URL } from \"./Context/config\";\r\n\r\nexport interface PostViewData {\r\n  userId: number;\r\n  country?: string;\r\n  gender?: string;\r\n  date?: string;\r\n}\r\n\r\nexport interface PostClickData {\r\n  userId: number;\r\n  linkId: number;\r\n  gender?: string;\r\n  country?: string;\r\n  category: string;\r\n  date?: string;\r\n}\r\n\r\nexport const GetClicks = async () => {\r\n  try {\r\n    const authToken = getAuthToken();\r\n\r\n    const response = await api.get(`${BASE_URL}/Analytics/GetClicks`, {\r\n      headers: {\r\n        Authorization: `Bearer ${authToken}`,\r\n        withCredentials: true,\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Error fetching clicks:\", error);\r\n    if (error.response) {\r\n      switch (error.response.status) {\r\n        case 401:\r\n          throw new Error(\"Authentication failed. Please log in again.\");\r\n        case 500:\r\n          throw new Error(\r\n            \"Server error while fetching analytics. Please try again later.\"\r\n          );\r\n        default:\r\n          throw new Error(\"Failed to fetch click analytics. Please try again.\");\r\n      }\r\n    }\r\n    throw new Error(\r\n      \"Network error. Please check your internet connection and try again.\"\r\n    );\r\n  }\r\n};\r\n\r\nexport const GetViews = async () => {\r\n  try {\r\n    const authToken = getAuthToken();\r\n\r\n    const response = await api.get(`${BASE_URL}/Analytics/GetViews`, {\r\n      headers: {\r\n        Authorization: `Bearer ${authToken}`,\r\n        withCredentials: true,\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Error fetching views:\", error);\r\n    if (error.response) {\r\n      switch (error.response.status) {\r\n        case 401:\r\n          throw new Error(\"Authentication failed. Please log in again.\");\r\n        case 500:\r\n          throw new Error(\r\n            \"Server error while fetching view analytics. Please try again later.\"\r\n          );\r\n        default:\r\n          throw new Error(\"Failed to fetch view analytics. Please try again.\");\r\n      }\r\n    }\r\n    throw new Error(\r\n      \"Network error. Please check your internet connection and try again.\"\r\n    );\r\n  }\r\n};\r\n\r\nexport const PostClick = async (click: PostClickData) => {\r\n  const Data = {\r\n    userId: click.userId,\r\n    linkId: click.linkId,\r\n    gender: click.gender,\r\n    country: click.country,\r\n    category: click.category,\r\n    date: click.date,\r\n  };\r\n\r\n  try {\r\n    await api.post(`${BASE_URL}/Analytics/CreateClick`, Data, {\r\n      headers: {\r\n        withCredentials: true,\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error posting click:\", error);\r\n    // Don't throw error for analytics - just log it to avoid disrupting user experience\r\n    if (error.response && error.response.status !== 500) {\r\n      console.warn(\r\n        \"Click tracking failed:\",\r\n        error.response.status,\r\n        error.response.data\r\n      );\r\n    }\r\n  }\r\n};\r\n\r\nexport const PostView = async (view: PostViewData) => {\r\n  const Data = {\r\n    userId: view.userId,\r\n    gender: view.gender,\r\n    country: view.country,\r\n    date: view.date,\r\n  };\r\n\r\n  try {\r\n    const response = await api.post(`${BASE_URL}/Analytics/CreateView`, Data, {\r\n      headers: {\r\n        withCredentials: true,\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error posting view:\", error);\r\n    // Don't throw error for analytics - just log it to avoid disrupting user experience\r\n    if (error.response && error.response.status !== 500) {\r\n      console.warn(\r\n        \"View tracking failed:\",\r\n        error.response.status,\r\n        error.response.data\r\n      );\r\n    }\r\n  }\r\n};\r\n\r\nfunction getAuthToken() {\r\n  const cookies = document.cookie.split(\";\");\r\n\r\n  for (let i = 0; i < cookies.length; i++) {\r\n    const cookie = cookies[i].trim();\r\n    if (cookie.startsWith(\"authToken=\")) {\r\n      return cookie.substring(\"authToken=\".length, cookie.length);\r\n    }\r\n  }\r\n\r\n  return null;\r\n}\r\n\r\nexport const calculateDailyViews = (data) => {\r\n  const dailyViews = {};\r\n\r\n  if (data[0].views && data[0].views.length > 0) {\r\n    data[0].views.forEach((view) => {\r\n      const date = new Date(view.date);\r\n\r\n      const formattedDate =\r\n        `${date.getDate().toString().padStart(2, \"0\")}/` +\r\n        `${(date.getMonth() + 1).toString().padStart(2, \"0\")}/` +\r\n        `${date.getFullYear()}`;\r\n\r\n      if (dailyViews[formattedDate]) {\r\n        dailyViews[formattedDate] += 1;\r\n      } else {\r\n        dailyViews[formattedDate] = 1;\r\n      }\r\n    });\r\n  }\r\n\r\n  const result = Object.keys(dailyViews).map((date) => ({\r\n    date,\r\n    totalClicks: dailyViews[date],\r\n  }));\r\n\r\n  return result;\r\n};\r\n\r\nexport const mostViewedCountry = (data) => {\r\n  const countryViews = {};\r\n\r\n  if (data && data.length > 0 && data[0].views && data[0].views.length > 0) {\r\n    data[0].views.forEach((countryView) => {\r\n      const country = countryView.country.trim(); // Remove leading/trailing whitespace\r\n\r\n      if (countryViews[country]) {\r\n        countryViews[country] += 1; // Count occurrences\r\n      } else {\r\n        countryViews[country] = 1;\r\n      }\r\n    });\r\n  }\r\n\r\n  let mostViewedCountry = null;\r\n  let highestViewCount = 0;\r\n\r\n  for (const country in countryViews) {\r\n    if (countryViews[country] > highestViewCount) {\r\n      highestViewCount = countryViews[country];\r\n      mostViewedCountry = country;\r\n    }\r\n  }\r\n\r\n  return mostViewedCountry;\r\n};\r\n\r\nexport const calculateDailyClicks = (data) => {\r\n  const dailyClicks = {};\r\n\r\n  data.forEach((item) => {\r\n    if (item.linkClicks && item.linkClicks.length > 0) {\r\n      item.linkClicks.forEach((click) => {\r\n        const date = new Date(click.date);\r\n\r\n        const formattedDate =\r\n          `${date.getDate().toString().padStart(2, \"0\")}/` +\r\n          `${(date.getMonth() + 1).toString().padStart(2, \"0\")}/` +\r\n          `${date.getFullYear()}`;\r\n\r\n        if (dailyClicks[formattedDate]) {\r\n          dailyClicks[formattedDate] += 1;\r\n        } else {\r\n          dailyClicks[formattedDate] = 1;\r\n        }\r\n      });\r\n    }\r\n  });\r\n\r\n  const result = Object.keys(dailyClicks).map((date) => ({\r\n    date,\r\n    totalClicks: dailyClicks[date],\r\n  }));\r\n\r\n  return result;\r\n};\r\n\r\nexport const calculateCountryClicks = (data) => {\r\n  const countryClicks = {};\r\n\r\n  data.forEach((item) => {\r\n    if (item.linkClicks && item.linkClicks.length > 0) {\r\n      item.linkClicks.forEach((click) => {\r\n        if (countryClicks[click.country]) {\r\n          countryClicks[click.country] += 1;\r\n        } else {\r\n          countryClicks[click.country] = 1;\r\n        }\r\n      });\r\n    }\r\n  });\r\n\r\n  const result = Object.keys(countryClicks).map((country) => ({\r\n    country,\r\n    totalClicks: countryClicks[country],\r\n  }));\r\n\r\n  return result;\r\n};\r\n\r\nexport const calculateGenderClicks = (data) => {\r\n  const genderClicks = {};\r\n\r\n  data.forEach((item) => {\r\n    if (item.linkClicks && item.linkClicks.length > 0) {\r\n      item.linkClicks.forEach((click) => {\r\n        if (genderClicks[click.gender]) {\r\n          genderClicks[click.gender] += 1;\r\n        } else {\r\n          genderClicks[click.gender] = 1;\r\n        }\r\n      });\r\n    }\r\n  });\r\n\r\n  const result = Object.keys(genderClicks).map((gender) => ({\r\n    gender,\r\n    totalClicks: genderClicks[gender],\r\n  }));\r\n\r\n  return result;\r\n};\r\n\r\nexport const MostClickedCountry = (data) => {\r\n  const countryClicks = {};\r\n\r\n  data.forEach((item) => {\r\n    if (item.countryClickCounts && item.countryClickCounts.length > 0) {\r\n      item.countryClickCounts.forEach((countryClick) => {\r\n        const country = countryClick.country;\r\n\r\n        if (countryClicks[country]) {\r\n          countryClicks[country] += countryClick.clickCount;\r\n        } else {\r\n          countryClicks[country] = countryClick.clickCount;\r\n        }\r\n      });\r\n    }\r\n  });\r\n\r\n  let mostClickedCountry = null;\r\n  let highestClickCount = 0;\r\n\r\n  for (const country in countryClicks) {\r\n    if (countryClicks[country] > highestClickCount) {\r\n      highestClickCount = countryClicks[country];\r\n      mostClickedCountry = country;\r\n    }\r\n  }\r\n\r\n  return mostClickedCountry;\r\n};\r\n\r\nexport const getClickCountsByCategory = (data) => {\r\n  const categories = [\r\n    \"Custom\",\r\n    \"Twitter\",\r\n    \"GitHub\",\r\n    \"Instagram\",\r\n    \"Facebook\",\r\n    \"LinkedIn\",\r\n  ];\r\n\r\n  const clickCountsByCategory = [];\r\n\r\n  categories.forEach((category) => {\r\n    const totalClickCount = data.reduce((total, item) => {\r\n      if (item.linkCategory === category) {\r\n        return total + item.clickCount;\r\n      }\r\n      return total;\r\n    }, 0);\r\n\r\n    clickCountsByCategory.push(totalClickCount);\r\n  });\r\n\r\n  return clickCountsByCategory;\r\n};\r\n\r\nexport const GetCategoryClickCounts = (data) => {\r\n  const categoryClickCounts = {};\r\n\r\n  data.forEach((item) => {\r\n    if (!categoryClickCounts[item.linkCategory])\r\n      categoryClickCounts[item.linkCategory] = 0;\r\n\r\n    categoryClickCounts[item.linkCategory] += item.clickCount;\r\n  });\r\n\r\n  // Convert the accumulated click counts into an array of objects\r\n  const result = Object.keys(categoryClickCounts).map((category) => {\r\n    var icon = category;\r\n    var color;\r\n\r\n    switch (category) {\r\n      case \"Twitter\":\r\n        icon = \"Twitter-circle\";\r\n        color = \"#212121\";\r\n        break;\r\n      case \"Custom\":\r\n        icon = \"Profile\";\r\n        color = \"#007BFF\";\r\n        break;\r\n      case \"Facebook\":\r\n        color = \"#5892d0\";\r\n        break;\r\n      case \"Youtube\":\r\n        color = \"#FF0000\";\r\n        break;\r\n      case \"Instagram\":\r\n        color = \"#D81B60\";\r\n        break;\r\n      case \"Github\":\r\n        color = \"#212121\";\r\n        break;\r\n      case \"LinkedIn\":\r\n        color = \"#00b9f1\";\r\n        break;\r\n      default:\r\n        color = \"#000000\";\r\n        break;\r\n    }\r\n\r\n    return {\r\n      name: category,\r\n      value: categoryClickCounts[category],\r\n      icon: icon.toLowerCase(),\r\n      color: color,\r\n    };\r\n  });\r\n\r\n  return result;\r\n};\r\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AACvB,SAASC,QAAQ,QAAQ,kBAAkB;AAkB3C,OAAO,MAAMC,SAAS,GAAG,MAAAA,CAAA,KAAY;EACnC,IAAI;IACF,MAAMC,SAAS,GAAGC,YAAY,CAAC,CAAC;IAEhC,MAAMC,QAAQ,GAAG,MAAML,GAAG,CAACM,GAAG,CAAC,GAAGL,QAAQ,sBAAsB,EAAE;MAChEM,OAAO,EAAE;QACPC,aAAa,EAAE,UAAUL,SAAS,EAAE;QACpCM,eAAe,EAAE;MACnB;IACF,CAAC,CAAC;IAEF,OAAOJ,QAAQ;EACjB,CAAC,CAAC,OAAOK,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAC9C,IAAIA,KAAK,CAACL,QAAQ,EAAE;MAClB,QAAQK,KAAK,CAACL,QAAQ,CAACO,MAAM;QAC3B,KAAK,GAAG;UACN,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;QAChE,KAAK,GAAG;UACN,MAAM,IAAIA,KAAK,CACb,gEACF,CAAC;QACH;UACE,MAAM,IAAIA,KAAK,CAAC,oDAAoD,CAAC;MACzE;IACF;IACA,MAAM,IAAIA,KAAK,CACb,qEACF,CAAC;EACH;AACF,CAAC;AAACC,EAAA,GA9BWZ,SAAS;AAgCtB,OAAO,MAAMa,QAAQ,GAAG,MAAAA,CAAA,KAAY;EAClC,IAAI;IACF,MAAMZ,SAAS,GAAGC,YAAY,CAAC,CAAC;IAEhC,MAAMC,QAAQ,GAAG,MAAML,GAAG,CAACM,GAAG,CAAC,GAAGL,QAAQ,qBAAqB,EAAE;MAC/DM,OAAO,EAAE;QACPC,aAAa,EAAE,UAAUL,SAAS,EAAE;QACpCM,eAAe,EAAE;MACnB;IACF,CAAC,CAAC;IAEF,OAAOJ,QAAQ;EACjB,CAAC,CAAC,OAAOK,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC7C,IAAIA,KAAK,CAACL,QAAQ,EAAE;MAClB,QAAQK,KAAK,CAACL,QAAQ,CAACO,MAAM;QAC3B,KAAK,GAAG;UACN,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;QAChE,KAAK,GAAG;UACN,MAAM,IAAIA,KAAK,CACb,qEACF,CAAC;QACH;UACE,MAAM,IAAIA,KAAK,CAAC,mDAAmD,CAAC;MACxE;IACF;IACA,MAAM,IAAIA,KAAK,CACb,qEACF,CAAC;EACH;AACF,CAAC;AAACG,GAAA,GA9BWD,QAAQ;AAgCrB,OAAO,MAAME,SAAS,GAAG,MAAOC,KAAoB,IAAK;EACvD,MAAMC,IAAI,GAAG;IACXC,MAAM,EAAEF,KAAK,CAACE,MAAM;IACpBC,MAAM,EAAEH,KAAK,CAACG,MAAM;IACpBC,MAAM,EAAEJ,KAAK,CAACI,MAAM;IACpBC,OAAO,EAAEL,KAAK,CAACK,OAAO;IACtBC,QAAQ,EAAEN,KAAK,CAACM,QAAQ;IACxBC,IAAI,EAAEP,KAAK,CAACO;EACd,CAAC;EAED,IAAI;IACF,MAAMzB,GAAG,CAAC0B,IAAI,CAAC,GAAGzB,QAAQ,wBAAwB,EAAEkB,IAAI,EAAE;MACxDZ,OAAO,EAAE;QACPE,eAAe,EAAE;MACnB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC5C;IACA,IAAIA,KAAK,CAACL,QAAQ,IAAIK,KAAK,CAACL,QAAQ,CAACO,MAAM,KAAK,GAAG,EAAE;MACnDD,OAAO,CAACgB,IAAI,CACV,wBAAwB,EACxBjB,KAAK,CAACL,QAAQ,CAACO,MAAM,EACrBF,KAAK,CAACL,QAAQ,CAACuB,IACjB,CAAC;IACH;EACF;AACF,CAAC;AAACC,GAAA,GA3BWZ,SAAS;AA6BtB,OAAO,MAAMa,QAAQ,GAAG,MAAOC,IAAkB,IAAK;EACpD,MAAMZ,IAAI,GAAG;IACXC,MAAM,EAAEW,IAAI,CAACX,MAAM;IACnBE,MAAM,EAAES,IAAI,CAACT,MAAM;IACnBC,OAAO,EAAEQ,IAAI,CAACR,OAAO;IACrBE,IAAI,EAAEM,IAAI,CAACN;EACb,CAAC;EAED,IAAI;IACF,MAAMpB,QAAQ,GAAG,MAAML,GAAG,CAAC0B,IAAI,CAAC,GAAGzB,QAAQ,uBAAuB,EAAEkB,IAAI,EAAE;MACxEZ,OAAO,EAAE;QACPE,eAAe,EAAE;MACnB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAC3C;IACA,IAAIA,KAAK,CAACL,QAAQ,IAAIK,KAAK,CAACL,QAAQ,CAACO,MAAM,KAAK,GAAG,EAAE;MACnDD,OAAO,CAACgB,IAAI,CACV,uBAAuB,EACvBjB,KAAK,CAACL,QAAQ,CAACO,MAAM,EACrBF,KAAK,CAACL,QAAQ,CAACuB,IACjB,CAAC;IACH;EACF;AACF,CAAC;AAACI,GAAA,GAzBWF,QAAQ;AA2BrB,SAAS1B,YAAYA,CAAA,EAAG;EACtB,MAAM6B,OAAO,GAAGC,QAAQ,CAACC,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC;EAE1C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,OAAO,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC,MAAMF,MAAM,GAAGF,OAAO,CAACI,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC;IAChC,IAAIJ,MAAM,CAACK,UAAU,CAAC,YAAY,CAAC,EAAE;MACnC,OAAOL,MAAM,CAACM,SAAS,CAAC,YAAY,CAACH,MAAM,EAAEH,MAAM,CAACG,MAAM,CAAC;IAC7D;EACF;EAEA,OAAO,IAAI;AACb;AAEA,OAAO,MAAMI,mBAAmB,GAAId,IAAI,IAAK;EAC3C,MAAMe,UAAU,GAAG,CAAC,CAAC;EAErB,IAAIf,IAAI,CAAC,CAAC,CAAC,CAACgB,KAAK,IAAIhB,IAAI,CAAC,CAAC,CAAC,CAACgB,KAAK,CAACN,MAAM,GAAG,CAAC,EAAE;IAC7CV,IAAI,CAAC,CAAC,CAAC,CAACgB,KAAK,CAACC,OAAO,CAAEd,IAAI,IAAK;MAC9B,MAAMN,IAAI,GAAG,IAAIqB,IAAI,CAACf,IAAI,CAACN,IAAI,CAAC;MAEhC,MAAMsB,aAAa,GACjB,GAAGtB,IAAI,CAACuB,OAAO,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAChD,GAAG,CAACzB,IAAI,CAAC0B,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAEF,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GACvD,GAAGzB,IAAI,CAAC2B,WAAW,CAAC,CAAC,EAAE;MAEzB,IAAIT,UAAU,CAACI,aAAa,CAAC,EAAE;QAC7BJ,UAAU,CAACI,aAAa,CAAC,IAAI,CAAC;MAChC,CAAC,MAAM;QACLJ,UAAU,CAACI,aAAa,CAAC,GAAG,CAAC;MAC/B;IACF,CAAC,CAAC;EACJ;EAEA,MAAMM,MAAM,GAAGC,MAAM,CAACC,IAAI,CAACZ,UAAU,CAAC,CAACa,GAAG,CAAE/B,IAAI,KAAM;IACpDA,IAAI;IACJgC,WAAW,EAAEd,UAAU,CAAClB,IAAI;EAC9B,CAAC,CAAC,CAAC;EAEH,OAAO4B,MAAM;AACf,CAAC;AAED,OAAO,MAAMK,iBAAiB,GAAI9B,IAAI,IAAK;EACzC,MAAM+B,YAAY,GAAG,CAAC,CAAC;EAEvB,IAAI/B,IAAI,IAAIA,IAAI,CAACU,MAAM,GAAG,CAAC,IAAIV,IAAI,CAAC,CAAC,CAAC,CAACgB,KAAK,IAAIhB,IAAI,CAAC,CAAC,CAAC,CAACgB,KAAK,CAACN,MAAM,GAAG,CAAC,EAAE;IACxEV,IAAI,CAAC,CAAC,CAAC,CAACgB,KAAK,CAACC,OAAO,CAAEe,WAAW,IAAK;MACrC,MAAMrC,OAAO,GAAGqC,WAAW,CAACrC,OAAO,CAACgB,IAAI,CAAC,CAAC,CAAC,CAAC;;MAE5C,IAAIoB,YAAY,CAACpC,OAAO,CAAC,EAAE;QACzBoC,YAAY,CAACpC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;MAC9B,CAAC,MAAM;QACLoC,YAAY,CAACpC,OAAO,CAAC,GAAG,CAAC;MAC3B;IACF,CAAC,CAAC;EACJ;EAEA,IAAImC,iBAAiB,GAAG,IAAI;EAC5B,IAAIG,gBAAgB,GAAG,CAAC;EAExB,KAAK,MAAMtC,OAAO,IAAIoC,YAAY,EAAE;IAClC,IAAIA,YAAY,CAACpC,OAAO,CAAC,GAAGsC,gBAAgB,EAAE;MAC5CA,gBAAgB,GAAGF,YAAY,CAACpC,OAAO,CAAC;MACxCmC,iBAAiB,GAAGnC,OAAO;IAC7B;EACF;EAEA,OAAOmC,iBAAiB;AAC1B,CAAC;AAED,OAAO,MAAMI,oBAAoB,GAAIlC,IAAI,IAAK;EAC5C,MAAMmC,WAAW,GAAG,CAAC,CAAC;EAEtBnC,IAAI,CAACiB,OAAO,CAAEmB,IAAI,IAAK;IACrB,IAAIA,IAAI,CAACC,UAAU,IAAID,IAAI,CAACC,UAAU,CAAC3B,MAAM,GAAG,CAAC,EAAE;MACjD0B,IAAI,CAACC,UAAU,CAACpB,OAAO,CAAE3B,KAAK,IAAK;QACjC,MAAMO,IAAI,GAAG,IAAIqB,IAAI,CAAC5B,KAAK,CAACO,IAAI,CAAC;QAEjC,MAAMsB,aAAa,GACjB,GAAGtB,IAAI,CAACuB,OAAO,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAChD,GAAG,CAACzB,IAAI,CAAC0B,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAEF,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GACvD,GAAGzB,IAAI,CAAC2B,WAAW,CAAC,CAAC,EAAE;QAEzB,IAAIW,WAAW,CAAChB,aAAa,CAAC,EAAE;UAC9BgB,WAAW,CAAChB,aAAa,CAAC,IAAI,CAAC;QACjC,CAAC,MAAM;UACLgB,WAAW,CAAChB,aAAa,CAAC,GAAG,CAAC;QAChC;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEF,MAAMM,MAAM,GAAGC,MAAM,CAACC,IAAI,CAACQ,WAAW,CAAC,CAACP,GAAG,CAAE/B,IAAI,KAAM;IACrDA,IAAI;IACJgC,WAAW,EAAEM,WAAW,CAACtC,IAAI;EAC/B,CAAC,CAAC,CAAC;EAEH,OAAO4B,MAAM;AACf,CAAC;AAED,OAAO,MAAMa,sBAAsB,GAAItC,IAAI,IAAK;EAC9C,MAAMuC,aAAa,GAAG,CAAC,CAAC;EAExBvC,IAAI,CAACiB,OAAO,CAAEmB,IAAI,IAAK;IACrB,IAAIA,IAAI,CAACC,UAAU,IAAID,IAAI,CAACC,UAAU,CAAC3B,MAAM,GAAG,CAAC,EAAE;MACjD0B,IAAI,CAACC,UAAU,CAACpB,OAAO,CAAE3B,KAAK,IAAK;QACjC,IAAIiD,aAAa,CAACjD,KAAK,CAACK,OAAO,CAAC,EAAE;UAChC4C,aAAa,CAACjD,KAAK,CAACK,OAAO,CAAC,IAAI,CAAC;QACnC,CAAC,MAAM;UACL4C,aAAa,CAACjD,KAAK,CAACK,OAAO,CAAC,GAAG,CAAC;QAClC;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEF,MAAM8B,MAAM,GAAGC,MAAM,CAACC,IAAI,CAACY,aAAa,CAAC,CAACX,GAAG,CAAEjC,OAAO,KAAM;IAC1DA,OAAO;IACPkC,WAAW,EAAEU,aAAa,CAAC5C,OAAO;EACpC,CAAC,CAAC,CAAC;EAEH,OAAO8B,MAAM;AACf,CAAC;AAED,OAAO,MAAMe,qBAAqB,GAAIxC,IAAI,IAAK;EAC7C,MAAMyC,YAAY,GAAG,CAAC,CAAC;EAEvBzC,IAAI,CAACiB,OAAO,CAAEmB,IAAI,IAAK;IACrB,IAAIA,IAAI,CAACC,UAAU,IAAID,IAAI,CAACC,UAAU,CAAC3B,MAAM,GAAG,CAAC,EAAE;MACjD0B,IAAI,CAACC,UAAU,CAACpB,OAAO,CAAE3B,KAAK,IAAK;QACjC,IAAImD,YAAY,CAACnD,KAAK,CAACI,MAAM,CAAC,EAAE;UAC9B+C,YAAY,CAACnD,KAAK,CAACI,MAAM,CAAC,IAAI,CAAC;QACjC,CAAC,MAAM;UACL+C,YAAY,CAACnD,KAAK,CAACI,MAAM,CAAC,GAAG,CAAC;QAChC;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEF,MAAM+B,MAAM,GAAGC,MAAM,CAACC,IAAI,CAACc,YAAY,CAAC,CAACb,GAAG,CAAElC,MAAM,KAAM;IACxDA,MAAM;IACNmC,WAAW,EAAEY,YAAY,CAAC/C,MAAM;EAClC,CAAC,CAAC,CAAC;EAEH,OAAO+B,MAAM;AACf,CAAC;AAED,OAAO,MAAMiB,kBAAkB,GAAI1C,IAAI,IAAK;EAC1C,MAAMuC,aAAa,GAAG,CAAC,CAAC;EAExBvC,IAAI,CAACiB,OAAO,CAAEmB,IAAI,IAAK;IACrB,IAAIA,IAAI,CAACO,kBAAkB,IAAIP,IAAI,CAACO,kBAAkB,CAACjC,MAAM,GAAG,CAAC,EAAE;MACjE0B,IAAI,CAACO,kBAAkB,CAAC1B,OAAO,CAAE2B,YAAY,IAAK;QAChD,MAAMjD,OAAO,GAAGiD,YAAY,CAACjD,OAAO;QAEpC,IAAI4C,aAAa,CAAC5C,OAAO,CAAC,EAAE;UAC1B4C,aAAa,CAAC5C,OAAO,CAAC,IAAIiD,YAAY,CAACC,UAAU;QACnD,CAAC,MAAM;UACLN,aAAa,CAAC5C,OAAO,CAAC,GAAGiD,YAAY,CAACC,UAAU;QAClD;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEF,IAAIC,kBAAkB,GAAG,IAAI;EAC7B,IAAIC,iBAAiB,GAAG,CAAC;EAEzB,KAAK,MAAMpD,OAAO,IAAI4C,aAAa,EAAE;IACnC,IAAIA,aAAa,CAAC5C,OAAO,CAAC,GAAGoD,iBAAiB,EAAE;MAC9CA,iBAAiB,GAAGR,aAAa,CAAC5C,OAAO,CAAC;MAC1CmD,kBAAkB,GAAGnD,OAAO;IAC9B;EACF;EAEA,OAAOmD,kBAAkB;AAC3B,CAAC;AAACE,GAAA,GA5BWN,kBAAkB;AA8B/B,OAAO,MAAMO,wBAAwB,GAAIjD,IAAI,IAAK;EAChD,MAAMkD,UAAU,GAAG,CACjB,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,WAAW,EACX,UAAU,EACV,UAAU,CACX;EAED,MAAMC,qBAAqB,GAAG,EAAE;EAEhCD,UAAU,CAACjC,OAAO,CAAErB,QAAQ,IAAK;IAC/B,MAAMwD,eAAe,GAAGpD,IAAI,CAACqD,MAAM,CAAC,CAACC,KAAK,EAAElB,IAAI,KAAK;MACnD,IAAIA,IAAI,CAACmB,YAAY,KAAK3D,QAAQ,EAAE;QAClC,OAAO0D,KAAK,GAAGlB,IAAI,CAACS,UAAU;MAChC;MACA,OAAOS,KAAK;IACd,CAAC,EAAE,CAAC,CAAC;IAELH,qBAAqB,CAACK,IAAI,CAACJ,eAAe,CAAC;EAC7C,CAAC,CAAC;EAEF,OAAOD,qBAAqB;AAC9B,CAAC;AAED,OAAO,MAAMM,sBAAsB,GAAIzD,IAAI,IAAK;EAC9C,MAAM0D,mBAAmB,GAAG,CAAC,CAAC;EAE9B1D,IAAI,CAACiB,OAAO,CAAEmB,IAAI,IAAK;IACrB,IAAI,CAACsB,mBAAmB,CAACtB,IAAI,CAACmB,YAAY,CAAC,EACzCG,mBAAmB,CAACtB,IAAI,CAACmB,YAAY,CAAC,GAAG,CAAC;IAE5CG,mBAAmB,CAACtB,IAAI,CAACmB,YAAY,CAAC,IAAInB,IAAI,CAACS,UAAU;EAC3D,CAAC,CAAC;;EAEF;EACA,MAAMpB,MAAM,GAAGC,MAAM,CAACC,IAAI,CAAC+B,mBAAmB,CAAC,CAAC9B,GAAG,CAAEhC,QAAQ,IAAK;IAChE,IAAI+D,IAAI,GAAG/D,QAAQ;IACnB,IAAIgE,KAAK;IAET,QAAQhE,QAAQ;MACd,KAAK,SAAS;QACZ+D,IAAI,GAAG,gBAAgB;QACvBC,KAAK,GAAG,SAAS;QACjB;MACF,KAAK,QAAQ;QACXD,IAAI,GAAG,SAAS;QAChBC,KAAK,GAAG,SAAS;QACjB;MACF,KAAK,UAAU;QACbA,KAAK,GAAG,SAAS;QACjB;MACF,KAAK,SAAS;QACZA,KAAK,GAAG,SAAS;QACjB;MACF,KAAK,WAAW;QACdA,KAAK,GAAG,SAAS;QACjB;MACF,KAAK,QAAQ;QACXA,KAAK,GAAG,SAAS;QACjB;MACF,KAAK,UAAU;QACbA,KAAK,GAAG,SAAS;QACjB;MACF;QACEA,KAAK,GAAG,SAAS;QACjB;IACJ;IAEA,OAAO;MACLC,IAAI,EAAEjE,QAAQ;MACdkE,KAAK,EAAEJ,mBAAmB,CAAC9D,QAAQ,CAAC;MACpC+D,IAAI,EAAEA,IAAI,CAACI,WAAW,CAAC,CAAC;MACxBH,KAAK,EAAEA;IACT,CAAC;EACH,CAAC,CAAC;EAEF,OAAOnC,MAAM;AACf,CAAC;AAACuC,GAAA,GArDWP,sBAAsB;AAAA,IAAAvE,EAAA,EAAAE,GAAA,EAAAa,GAAA,EAAAG,GAAA,EAAA4C,GAAA,EAAAgB,GAAA;AAAAC,YAAA,CAAA/E,EAAA;AAAA+E,YAAA,CAAA7E,GAAA;AAAA6E,YAAA,CAAAhE,GAAA;AAAAgE,YAAA,CAAA7D,GAAA;AAAA6D,YAAA,CAAAjB,GAAA;AAAAiB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}