import api from "./Api";
import { BASE_URL } from "./Context/config";

export interface PostSocialLinkData {
  ProfileId: number;
  LinkUrl: string;
  Category: string;
  Title: string;
  Color: string;
}

export interface PostCustomLinkData {
  ProfileId: number;
  LinkUrl: string;
  Title: string;
  Icon: string;
}

export interface PutCustomLinkData {
  Id: number;
  ProfileId: number;
  LinkUrl: string;
  Title: string;
  Icon: string;
}

export interface PutSocialLinkData {
  Id: number;
  ProfileId: number;
  LinkUrl: string;
  Title: string;
  Category: string;
  Color: string;
}

export const CreateSocialLink = async (link: PostSocialLinkData) => {
  const data = {
    ProfileId: link.ProfileId,
    LinkUrl: link.LinkUrl,
    Category: link.Category,
    Title: link.Title,
    Color: link.Color,
  };

  try {
    var authToken = getAuthToken();
    const response = await api.post(
      `${BASE_URL}/Links/CreateSocialLink`,
      data,
      {
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    return response;
  } catch (error) {
    console.error("Error creating social link:", error);
    if (error.response && error.response.data) {
      if (typeof error.response.data === "string") {
        throw new Error(error.response.data);
      }
      if (error.response.data.error) {
        throw new Error(error.response.data.error);
      }
    }
    // Provide user-friendly error messages based on status codes
    if (error.response) {
      switch (error.response.status) {
        case 400:
          throw new Error(
            "Invalid social link data. Please check your information and try again."
          );
        case 401:
          throw new Error("Authentication failed. Please log in again.");
        case 409:
          throw new Error("A social link with this URL already exists.");
        case 500:
          throw new Error(
            "Server error while creating social link. Please try again later."
          );
        default:
          throw new Error("Failed to create social link. Please try again.");
      }
    }
    throw new Error(
      "Network error. Please check your internet connection and try again."
    );
  }
};

export const GetSocialLinks = async () => {
  try {
    var authToken = getAuthToken();

    const response = await api.get(`${BASE_URL}/Links/GetSocialLinks`, {
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
      },
    });

    return response;
  } catch (error) {
    console.error("Error:", error.message);
  }
};

export const EditSocialLink = async (link: PutSocialLinkData) => {
  const data = {
    Id: link.Id,
    ProfileId: link.ProfileId,
    Title: link.Title,
    LinkUrl: link.LinkUrl,
    Category: link.Category,
    Color: link.Color,
  };

  try {
    var authToken = getAuthToken();
    const response = await api.put(`${BASE_URL}/Links/EditSocialLink`, data, {
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
      },
    });

    return response;
  } catch (error) {
    console.error("Error editing social link:", error);
    if (error.response && error.response.data) {
      if (typeof error.response.data === "string") {
        throw new Error(error.response.data);
      }
      if (error.response.data.error) {
        throw new Error(error.response.data.error);
      }
    }
    // Provide user-friendly error messages based on status codes
    if (error.response) {
      switch (error.response.status) {
        case 400:
          throw new Error(
            "Invalid social link data. Please check your information and try again."
          );
        case 401:
          throw new Error("Authentication failed. Please log in again.");
        case 404:
          throw new Error("Social link not found. It may have been deleted.");
        case 500:
          throw new Error(
            "Server error while updating social link. Please try again later."
          );
        default:
          throw new Error("Failed to update social link. Please try again.");
      }
    }
    throw new Error(
      "Network error. Please check your internet connection and try again."
    );
  }
};

export const DeleteSocialLink = async (Id: number) => {
  try {
    var authToken = getAuthToken();
    const response = await api.delete(
      `${BASE_URL}/Links/DeleteSocialLink/` + Id,
      {
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    return response;
  } catch (error) {
    console.error("Error:", error.message);
    throw error; // Re-throw the error so it can be handled by the calling function
  }
};

export const CreateCustomLink = async (link: PostCustomLinkData) => {
  const data = {
    ProfileId: link.ProfileId,
    LinkUrl: link.LinkUrl,
    Title: link.Title,
    Icon: link.Icon,
  };

  try {
    var authToken = getAuthToken();
    const response = await api.post(
      `${BASE_URL}/Links/CreateCustomLink`,
      data,
      {
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    return response;
  } catch (error) {
    console.error("Error:", error.message);
  }
};

export const GetCustomLinks = async () => {
  try {
    var authToken = getAuthToken();

    const response = await api.get(`${BASE_URL}/Links/GetCustomLinks`, {
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
      },
    });

    return response;
  } catch (error) {
    console.error("Error:", error.message);
  }
};

export const EditCustomLink = async (link: PutCustomLinkData) => {
  const data = {
    Id: link.Id,
    Title: link.Title,
    ProfileId: link.ProfileId,
    LinkUrl: link.LinkUrl,
    Icon: link.Icon,
  };

  try {
    var authToken = getAuthToken();
    const response = await api.put(`${BASE_URL}/Links/EditCustomLink`, data, {
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
      },
    });

    return response;
  } catch (error) {
    return { error: error.message };
  }
};

export const DeleteCustomLink = async (Id: number) => {
  try {
    var authToken = getAuthToken();
    const response = await api.delete(
      `${BASE_URL}/Links/DeleteCustomLink/` + Id,
      {
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    return response;
  } catch (error) {
    console.error("Error:", error.message);
    throw error; // Re-throw the error so it can be handled by the calling function
  }
};

export function getAuthToken() {
  const cookies = document.cookie.split(";");

  for (let i = 0; i < cookies.length; i++) {
    const cookie = cookies[i].trim();
    if (cookie.startsWith("authToken=")) {
      return cookie.substring("authToken=".length, cookie.length);
    }
  }

  return null;
}
